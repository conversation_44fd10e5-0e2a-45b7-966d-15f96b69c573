// packages/types/src/events/sms.events.payload.ts
import type { BaseEventPayload } from './base-event.payload';

export interface SmsSendRequestPayload extends BaseEventPayload {
  data: {
    messageId: string; // Client-generated or internal ID for tracking
    toPhoneNumber: string;
    messageContent: string;
    relatedCallId?: string;
  };
}

export interface SmsSentPayload extends BaseEventPayload {
  data: {
    originalMessageId: string;
    gatewayReferenceId?: string;
    sentTimestamp: string;
  };
}

export interface SmsFailedPayload extends BaseEventPayload {
  data: {
    originalMessageId: string;
    reason: string;
    errorCode?: string;
  };
}