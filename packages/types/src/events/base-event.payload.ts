export interface BaseEventPayload {
  eventId: string; // UUID for idempotency and tracing
  timestamp: string; // ISO 8601
  tenantId: string;
  sourceService: string; // e.g., "CallCommandService", "PbxIntegrationService"
  correlationId?: string; // To trace a flow across multiple events
  actor?: { // Who or what initiated the action leading to this event
    type: 'USER' | 'OPERATOR' | 'SYSTEM' | 'BOT' | 'PBX';
    id?: string; // User ID, Operator ID, Bot ID, System Process Name
  };
}