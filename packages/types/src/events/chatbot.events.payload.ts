// packages/types/src/events/chatbot.events.payload.ts
import type { BaseEventPayload } from './base-event.payload';
// You might need to import specific types from 'gramio' or '@gramio/types'
// For example, if you want to include the raw Telegram Update object:
import type { Update as TelegramUpdate, Message as TelegramMessage } from 'gramio';

export interface ChatbotMessageReceivedPayload extends BaseEventPayload {
  data: {
    platform: 'telegram' | 'viber' | 'whatsapp'; // etc.
    botId: string; // Your internal ID for the bot instance
    chatId: string | number;
    userId: string | number;
    messageId: string | number;
    text?: string;
    timestamp: string; // ISO 8601 of message
    rawMessage?: TelegramUpdate; // Optional: include the raw platform message
    // Extracted entities, intents, etc., if NLP is done before publishing
    nlpResult?: {
      intent?: string;
      entities?: Record<string, any>;
    };
    // Any other standardized fields you want across platforms
  };
}

export interface ChatbotReplyToSendPayload extends BaseEventPayload {
    data: {
        platform: 'telegram' | 'viber' | 'whatsapp';
        botId: string;
        chatId: string | number;
        userId: string | number;
        text: string;
        replyToMessageId?: string | number;
        keyboard?: any; // Platform-specific keyboard structure
    }
}