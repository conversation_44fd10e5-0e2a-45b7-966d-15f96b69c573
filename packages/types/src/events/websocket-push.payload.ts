// packages/types/src/events/websocket-push.payload.ts
// These are NOT BaseEventPayload as they are internal to the push service

export interface WsPushCallListUpdatePayload {
  tenantId: string;
  type: 'full' | 'delta'; // Full list or just changes
  calls: any[]; // Array of PbxCall DTOs/simplified objects for UI
}

export interface WsPushCallDetailUpdatePayload {
  tenantId: string;
  call: any; // Single PbxCall DTO/simplified object
}

export interface WsPushNotificationPayload {
  tenantId: string;
  userId?: string; // If targeted to a specific user
  notification: {
    id: string;
    type: 'info' | 'warning' | 'error' | 'success' | 'sms_received' | 'call_missed';
    title: string;
    message: string;
    timestamp: string;
    actions?: Array<{ label: string; eventToEmitOnClick?: string; eventPayload?: any }>;
  };
}