// packages/types/src/events/pbx.events.payload.ts
import type { BaseEventPayload } from './base-event.payload';

export interface PbxCallInfo {
  pbxCallId: string; // Call ID from the PBX system
  fromPhoneNumber: string;
  toExtension: string; // Or target number
  direction: 'inbound' | 'outbound';
  // Add any other raw details from PBX needed by downstream consumers
}

export interface PbxCallIncomingPayload extends BaseEventPayload {
  data: PbxCallInfo & {
    callerName?: string; // If available from PBX
  };
}

export interface PbxCallAnsweredPayload extends BaseEventPayload {
  data: PbxCallInfo & {
    answeredByExtension: string;
    // Potentially operatorId if PBX knows which operator picked up
  };
}

export interface PbxCallTerminatedPayload extends BaseEventPayload {
  data: PbxCallInfo & {
    durationSeconds: number;
    reason?: string; // e.g., 'caller_hangup', 'callee_hangup', 'no_answer'
  };
}

export interface PbxCallHoldPayload extends BaseEventPayload {
  data: PbxCallInfo & {
    hold: boolean; // true for hold, false for resume
  };
}