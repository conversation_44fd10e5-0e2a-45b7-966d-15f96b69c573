// packages/types/src/events/call.events.payload.ts
import type { BaseEventPayload } from './base-event.payload';
import type { PbxCall } from '@repo/db'; // Assuming Drizzle type

export interface CallCreatedPayload extends BaseEventPayload {
  data: PbxCall; // The full call object as created in the DB
}

export interface CallUpdatedPayload extends BaseEventPayload {
  data: {
    callId: string; // UUID of the PbxCall
    updatedFields: Partial<PbxCall>; // Only the fields that changed
    previousFields?: Partial<PbxCall>; // Optional: for audit or complex logic
  };
}

export interface CallVehicleAssignedPayload extends BaseEventPayload {
  data: {
    callId: string;
    vehicleId: string; // Or vehicle number
    assignedByOperatorId?: string;
    assignmentTimestamp: string; // ISO 8601
    estimatedArrivalTimeMinutes?: number;
  };
}