{"name": "@repo/types", "version": "0.1.0", "private": true, "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsc", "typecheck": "tsc --noEmit", "lint": "echo 'Skipping lint for @repo/types'", "format": "biome format src --write", "test": "echo 'No tests yet'", "clean": "rm -rf dist .turbo tsconfig.tsbuildinfo"}, "dependencies": {"@repo/typescript-config": "workspace:^", "@repo/db": "workspace:^"}, "peerDependencies": {"gramio": "^0.4.3"}, "devDependencies": {"typescript": "^5.8.3"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./events": {"import": "./dist/events/index.js", "require": "./dist/events/index.js", "types": "./dist/events/index.d.ts"}, "./package.json": "./package.json"}, "imports": {"#*": "./dist/*"}}