import { relations, sql } from "drizzle-orm";
import {
	index,
	pgTable,
	text,
	timestamp,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { actorTypeEnum } from "../enums/actor-type.enum.js";
import { tenants } from "./tenants.schema.js";
import { users } from "./users.schema.js";

export const auditLogs = pgTable(
	"audit_logs", // Renamed table to plural
	{
		id: uuid("id").primaryKey().defaultRandom(),
		actorId: uuid("actor_id"), // Nullable if actor can be deleted and logs retained
		actorType: actorTypeEnum("actor_type").notNull(), // Ensure enum name is "actors_type_enum" or similar generic name
		tenantId: uuid("tenant_id").references(() => tenants.id, {
			onDelete: "set null",
		}), // Nullable for system-wide logs
		eventType: varchar("event_type", { length: 255 }).notNull(),
		targetTable: varchar("target_table", { length: 255 }), // Nullable if not always applicable
		targetId: uuid("target_id"), // Nullable if not always applicable
		description: text("description"),
		details: text("details"), // Consider jsonb for structured details: jsonb("details").$type<Record<string, any>>()
		createdAt: timestamp("created_at").defaultNow().notNull(),
		updatedAt: timestamp("updated_at")
			.defaultNow()
			.notNull()
			.$onUpdate(() => new Date()), // Usually audit logs are immutable, but added for consistency if needed
		deletedAt: timestamp("deleted_at"), // For soft deletes if required
	},
	(table) => [
		index("audit_logs_tenant_id_idx").on(table.tenantId),
		index("audit_logs_actor_id_actor_type_idx").on(
			table.actorId,
			table.actorType,
		),
		index("audit_logs_event_type_idx").on(table.eventType),
		index("audit_logs_target_table_target_id_idx").on(
			table.targetTable,
			table.targetId,
		),
		index("audit_logs_created_at_idx").on(table.createdAt),
		index("audit_logs_deleted_at_idx")
			.on(table.deletedAt)
			.where(sql`${table.deletedAt} IS NOT NULL`),
	],
);

export const auditLogsRelations = relations(auditLogs, ({ one }) => ({
	tenant: one(tenants, {
		fields: [auditLogs.tenantId],
		references: [tenants.id],
	}),
	actor: one(users, {
		fields: [auditLogs.actorId],
		references: [users.id],
		relationName: "auditLogActor", // Optional: more descriptive relation name
		// onDelete: "set null" // If user is deleted, set actorId to null in logs
	}),
}));

/*
Note: The actor can be a user, operator, or system. Consider separate relations for each if needed,
or a more generic approach if actor_id can point to different tables based on actor_type.
For now, linking to `users` is a common starting point.
*/

export type AuditLog = typeof auditLogs.$inferSelect;
export type NewAuditLog = typeof auditLogs.$inferInsert;
