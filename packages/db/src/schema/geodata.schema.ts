import { relations, sql } from "drizzle-orm";
import {
	doublePrecision,
	geometry,
	index,
	jsonb,
	pgTable,
	timestamp,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { SOURCE_DB_ENUM } from "../enums/source.enum.js";
import { addresses } from "./addresses.schema.js"; // Added for addressId
import { areas } from "./area.schema.js";
import { tenants } from "./tenants.schema.js";
import { users } from "./users.schema.js";

export const geodataEntries = pgTable(
	"geodata_entries",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		tenantId: uuid("tenant_id").references(() => tenants.id, {
			onDelete: "cascade",
		}),
		addressId: uuid("address_id").references(() => addresses.id, {
			onDelete: "set null",
		}),
		country: varchar("country", { length: 2 }),
		city: varchar("city", { length: 255 }),
		address: varchar("address", { length: 255 }),
		geom: geometry("geom", {
			srid: 4326,
			type: "point",
			mode: "xy",
		}).notNull(),
		source: SOURCE_DB_ENUM("source").notNull(),
		userId: uuid("user_id").references(() => users.id, {
			onDelete: "set null",
		}),
		accuracy: doublePrecision("accuracy"),
		areaId: uuid("area_id").references(() => areas.id, {
			onDelete: "set null",
		}),
		osmTags: jsonb("osm_tags").$type<Record<string, any>>(),
		metadata: jsonb("metadata").$type<Record<string, any>>(),
		createdAt: timestamp("created_at").defaultNow().notNull(),
		updatedAt: timestamp("updated_at")
			.defaultNow()
			.notNull()
			.$onUpdate(() => new Date()),
		deletedAt: timestamp("deleted_at"),
	},
	(table) => [
		index("geodata_entries_tenant_id_idx").on(table.tenantId),
		index("geodata_entries_address_id_idx").on(table.addressId),
		index("geodata_entries_user_id_idx").on(table.userId),
		index("geodata_entries_area_id_idx").on(table.areaId),
		index("geodata_entries_source_idx").on(table.source),
		index("geodata_entries_geom_idx")
			.using("gist", table.geom)
			.where(sql`${table.geom} IS NOT NULL`),
		index("geodata_entries_deleted_at_idx")
			.on(table.deletedAt)
			.where(sql`${table.deletedAt} IS NOT NULL`),
	],
);

export const geodataEntriesRelations = relations(geodataEntries, ({ one }) => ({
	tenant: one(tenants, {
		fields: [geodataEntries.tenantId],
		references: [tenants.id],
		relationName: "tenantGeodata",
	}),
	user: one(users, {
		fields: [geodataEntries.userId],
		references: [users.id],
		relationName: "userGeodata",
	}),
	area: one(areas, {
		fields: [geodataEntries.areaId],
		references: [areas.id],
		relationName: "areaGeodata",
	}),
	canonicalAddress: one(addresses, {
		// New relation
		fields: [geodataEntries.addressId],
		references: [addresses.id],
		relationName: "canonicalAddressForGeodata",
	}),
}));

export type GeodataEntry = typeof geodataEntries.$inferSelect;
export type NewGeodataEntry = typeof geodataEntries.$inferInsert;
