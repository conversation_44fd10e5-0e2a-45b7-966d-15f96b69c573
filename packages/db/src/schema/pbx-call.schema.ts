import { relations, sql } from "drizzle-orm";
import {
	integer,
	index, // Added index
	pgTable,
	timestamp,
	uniqueIndex, // Added uniqueIndex
	uuid, // Moved uuid import
	varchar, // Moved varchar import
} from "drizzle-orm/pg-core"; // Group imports
import { callDirectionDbEnum } from "../enums/call-direction.enum.js"; // Ensure this file exports callDirectionEnum only
import { callStatusDbEnum } from "../enums/call-status.enum.js";
import { operators } from "./operators.schema.js";
import { rides } from "./rides.schema.js"; // Ensure this file exists
import { tenants } from "./tenants.schema.js";
import { users } from "./users.schema.js";
import { jsonb } from "drizzle-orm/pg-core";

export const pbxCalls = pgTable("pbx_call", {
	id: uuid("id").primaryKey().defaultRandom(),
	tenantId: uuid("tenant_id")
		.references(() => tenants.id)
		.notNull(),
	userId: uuid("user_id").references(() => users.id), // External party (e.g., customer)
	operatorId: uuid("operator_id").references(() => operators.id), // Internal agent/operator
	rideId: uuid("ride_id").references(() => rides.id), // If call is associated with a ride

	// Core call attributes
	direction: callDirectionDbEnum("direction").notNull(),
	status: callStatusDbEnum("status").notNull(),
	duration: integer("duration"), // In seconds
	recordingUrl: varchar("recording_url", { length: 1024 }),
	startedAt: timestamp("started_at").notNull(), // Timestamp when the call initiated or hit the system
	answeredAt: timestamp("answered_at"), // Timestamp when the call was actually answered
	endedAt: timestamp("ended_at"),

	// Attributes from PBX system / event payloads
	externalPbxId: varchar("external_pbx_id", { length: 255 }), // Call ID from the PBX system itself
	fromPhoneNumber: varchar("from_phone_number", { length: 50 }),
	toPhoneNumber: varchar("to_phone_number", { length: 50 }), // Could be the dialed extension or target number
	callerName: varchar("caller_name", { length: 255 }), // Caller's name if provided by PBX (e.g., CNAM)
	terminationReason: varchar("termination_reason", { length: 100 }), // e.g., caller_hangup, callee_hangup, no_answer, transfer, busy
	dispositionCode: varchar("disposition_code", { length: 100 }), // PBX-specific disposition code, if available
	pbxRawDetails: jsonb("pbx_raw_details"), // For storing raw PBX event data like channel variables
}, (table) => ({
	tenantIdx: index("pbx_call_tenant_id_idx").on(table.tenantId),
	userIdx: index("pbx_call_user_id_idx").on(table.userId).where(sql`${table.userId} IS NOT NULL`),
	operatorIdx: index("pbx_call_operator_id_idx").on(table.operatorId).where(sql`${table.operatorId} IS NOT NULL`),
	rideIdx: index("pbx_call_ride_id_idx").on(table.rideId).where(sql`${table.rideId} IS NOT NULL`),
	statusIdx: index("pbx_call_status_idx").on(table.status),
	startedAtIdx: index("pbx_call_started_at_idx").on(table.startedAt),
	fromPhoneNumberIdx: index("pbx_call_from_phone_number_idx").on(table.fromPhoneNumber).where(sql`${table.fromPhoneNumber} IS NOT NULL`),
	toPhoneNumberIdx: index("pbx_call_to_phone_number_idx").on(table.toPhoneNumber).where(sql`${table.toPhoneNumber} IS NOT NULL`),
	externalPbxIdUk: uniqueIndex("pbx_call_tenant_external_pbx_id_uk").on(table.tenantId, table.externalPbxId).where(sql`${table.externalPbxId} IS NOT NULL`),
}));

export const pbxCallsRelations = relations(pbxCalls, ({ one }) => ({
	tenant: one(tenants, {
		fields: [pbxCalls.tenantId],
		references: [tenants.id],
	}),
	user: one(users, {
		fields: [pbxCalls.userId],
		references: [users.id],
	}),
	operator: one(operators, {
		fields: [pbxCalls.operatorId],
		references: [operators.id],
	}),
	ride: one(rides, {
		fields: [pbxCalls.rideId],
		references: [rides.id],
	}),
}));

export type PbxCall = typeof pbxCalls.$inferSelect;
export type NewPbxCall = typeof pbxCalls.$inferInsert;
