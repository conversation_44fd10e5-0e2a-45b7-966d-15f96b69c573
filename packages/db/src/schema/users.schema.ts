import { sql } from "drizzle-orm";
import {
	boolean,
	doublePrecision,
	geometry,
	index,
	json,
	pgTable,
	timestamp,
	uniqueIndex,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";

export const users = pgTable(
	"users",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		phone: varchar("phone", { length: 20 }).notNull().unique(),
		email: varchar("email", { length: 255 }).unique(),
		external_id: varchar("external_id", { length: 255 }).unique(),
		verified: boolean("verified").notNull().default(false),
		registered_at: timestamp("registered_at").notNull().defaultNow(),
		last_login_at: timestamp("last_login_at"),
		legal_name: varchar("legal_name", { length: 255 }),
		display_name: varchar("display_name", { length: 255 }),
		date_of_birth: timestamp("date_of_birth"),
		language: varchar("language", { length: 10 }),
		avatar_url: varchar("avatar_url", { length: 1024 }),
		communication_opt_in: json("communication_opt_in").$type<
			Record<string, boolean>
		>(), // Example type
		privacy_flags: json("privacy_flags").$type<Record<string, boolean>>(), // Example type

		// --- GEOSPATIAL ADDITIONS ---
		currentLocation: geometry("current_location", {
			type: "point",
			mode: "xy", // Ensure this is "xy"
			srid: 4326,
		}),
		locationAccuracy: doublePrecision("location_accuracy"),
		locationUpdatedAt: timestamp("location_updated_at"),
		// --- END GEOSPATIAL ADDITIONS ---

		created_at: timestamp("created_at").defaultNow().notNull(), // Added not null
		updated_at: timestamp("updated_at")
			.defaultNow()
			.notNull()
			.$onUpdate(() => new Date()), // Added not null and onUpdate
		deleted_at: timestamp("deleted_at"),
		metadata: json("metadata").$type<Record<string, any>>(), // Example type
	},
	(table) => ({
		indexes: [
			uniqueIndex("users_phone_idx").on(table.phone), // Changed to users_phone_idx
			uniqueIndex("users_email_idx").on(table.email), // Changed to users_email_idx
			uniqueIndex("users_external_id_idx").on(table.external_id),
			index("users_deleted_at_idx")
				.on(table.deleted_at)
				.where(sql`${table.deleted_at} IS NOT NULL`), // Filtered index
			// --- GEOSPATIAL INDEX ---
			index("users_current_location_idx").using("gist", table.currentLocation)
				.where(sql`${table.currentLocation} IS NOT NULL`),
			// --- END GEOSPATIAL INDEX ---
		],
	}),
);

export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
