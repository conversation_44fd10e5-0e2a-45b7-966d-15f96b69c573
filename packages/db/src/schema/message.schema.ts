import { relations, sql } from "drizzle-orm";
import {
	boolean,
	doublePrecision,
	geometry,
	index,
	jsonb,
	pgTable,
	text,
	timestamp,
	uuid,
	varchar,
} from "drizzle-orm/pg-core"; // Added text, boolean, geometry, index, sql, PgTable
import { messageDirectionEnum } from "../enums/message-direction.enum.js";
import { messageTypeDbEnum } from "../enums/message-type.enum.js";
import { providerEnum } from "../enums/provider.enum.js";
import { smsStatusEnum } from "../enums/sms-status.enum.js"; // New Enum for SMS
import { chats } from "./chat.schema.js";
import { pbxCalls } from "./pbx-call.schema.js"; // For relation
import { tenants } from "./tenants.schema.js";
import { users } from "./users.schema.js";


const messagesColumns = {
	id: uuid("id").primaryKey().defaultRandom(),
	tenantId: uuid("tenant_id")
		.references(() => tenants.id, { onDelete: "cascade" })
		.notNull(),
	chatId: uuid("chat_id") // Assuming a chat context is always created, even for SMS. If not, make nullable.
		.references(() => chats.id, { onDelete: "cascade" })
		.notNull(),
	fromUserId: uuid("from_user_id")
		.references(() => users.id, { onDelete: "set null" })
		.notNull(),
	toUserId: uuid("to_user_id").references(() => users.id, { onDelete: "set null" }),

	// Identifiers & Channel
	providerMessageId: varchar("provider_message_id", { length: 255 }),
	direction: messageDirectionEnum("direction").notNull(),
	viaChannel: providerEnum("via_channel").notNull(), // e.g., 'SMS', 'TELEGRAM', 'CHAT_WIDGET'
	messageType: messageTypeDbEnum("message_type").notNull(), // e.g., 'SMS', 'TEXT', 'IMAGE'
	content: text("content").notNull(), // Assuming content can be longer than varchar(255)

	// Contact / Verification related (mostly from chatbot context, may be null for other types)
	phoneVerified: boolean("phone_verified"),
	verificationReference: varchar("verification_reference", { length: 255 }),

    // --- CORRECTED GEOLOCATION ---
	geolocation: geometry("geolocation", {
        srid: 4326,
        type: "point",
        mode: "xy" // Changed from "geography" to "xy"
    }), // Nullable if location is optional
    accuracy: doublePrecision("accuracy"), // Optional: accuracy of the geolocation in meters
    // --- END GEOLOCATION ---

	// SMS Specific & Relations
	smsStatus: smsStatusEnum("sms_status"), // Nullable, populated for SMS messages
	relatedPbxCallId: uuid("related_pbx_call_id").references(() => pbxCalls.id, { onDelete: "set null" }),
	repliedToMessageId: uuid("replied_to_message_id").references((): any => messages.id, { onDelete: "set null" }),
	errorFlag: boolean("error_flag").default(false),
	metadata: jsonb("metadata").$type<Record<string, any>>(),
	sentAt: timestamp("sent_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull().$onUpdate(() => new Date()),
	deletedAt: timestamp("deleted_at"),
};

// Add sender/recipient identifiers if fromUserId/toUserId are not sufficient for all channels (e.g. raw phone numbers for SMS)
// messagesColumns.senderIdentifier = varchar("sender_identifier", { length: 100 }); // e.g. phone number, bot name
// messagesColumns.recipientIdentifier = varchar("recipient_identifier", { length: 100 }); // e.g. phone number


export const messages = pgTable(
	"messages",
	messagesColumns,
	(table) => [
		index("messages_tenant_id_idx").on(table.tenantId),
		index("messages_chat_id_idx").on(table.chatId),
		index("messages_from_user_id_idx").on(table.fromUserId),
		index("messages_to_user_id_idx").on(table.toUserId),
		index("messages_sent_at_idx").on(table.sentAt),
		index("messages_message_type_idx").on(table.messageType),
		index("messages_geolocation_idx").using("gist", table.geolocation).where(sql`${table.geolocation} IS NOT NULL`), // Index only non-null locations
		index("messages_via_channel_idx").on(table.viaChannel),
		index("messages_sms_status_idx").on(table.smsStatus).where(sql`${table.smsStatus} IS NOT NULL`),
		index("messages_related_pbx_call_id_idx").on(table.relatedPbxCallId).where(sql`${table.relatedPbxCallId} IS NOT NULL`),
		index("messages_deleted_at_idx")
			.on(table.deletedAt)
			.where(sql`${table.deletedAt} IS NOT NULL`),
	]
);

export const messagesRelations = relations(messages, ({ one, many }) => ({ // Added many for replies
	tenant: one(tenants, {
		fields: [messages.tenantId],
		references: [tenants.id],
	}),
	chat: one(chats, {
		fields: [messages.chatId],
		references: [chats.id],
	}),
	fromUser: one(users, {
		fields: [messages.fromUserId],
		references: [users.id],
		relationName: "messageSender",
	}),
	toUser: one(users, {
		fields: [messages.toUserId],
		references: [users.id],
		relationName: "messageReceiver",
	}),
	repliedTo: one(messages, {
		fields: [messages.repliedToMessageId],
		references: [messages.id],
		relationName: "originalMessage",
	}),
    replies: many(messages, { // If a message can have multiple replies
        relationName: "messageReplies", // Needs a foreign key on the replies pointing back
    }),
	relatedPbxCall: one(pbxCalls, {
		fields: [messages.relatedPbxCallId],
		references: [pbxCalls.id],
		relationName: "relatedPbxCallForMessage",
	}),
}));

export type Message = typeof messages.$inferSelect; // geolocation will be { x: number, y: number } | null
export type NewMessage = typeof messages.$inferInsert; // geolocation should be { x: number, y: number } | null