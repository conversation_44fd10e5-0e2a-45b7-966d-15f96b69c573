import { relations } from "drizzle-orm";
import {
	date,
	index,
	json,
	pgTable,
	timestamp,
	uniqueIndex,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { kycStatusEnum } from "../enums/kyc-status.enum.js";
import { tenants } from "./tenants.schema.js";
import { users } from "./users.schema.js"; // Corrected import

export const userKyc = pgTable(
	"user_kyc",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		tenantId: uuid("tenant_id") // Nullable if KYC can be global
			.references(() => tenants.id, { onDelete: "cascade" }),
		userId: uuid("user_id")
			.references(() => users.id)
			.notNull(),
		provider: varchar("provider", { length: 255 }).notNull(),
		submittedAt: timestamp("submitted_at").notNull().defaultNow(),
		status: kycStatusEnum("status").notNull(),
		documentType: varchar("document_type", { length: 255 }).notNull(),
		documentNumber: varchar("document_number", { length: 255 }).notNull(),
		country: varchar("country", { length: 100 }).notNull(),
		expiryDate: date("expiry_date"),
		rejectionReason: varchar("rejection_reason", { length: 1024 }),
		metadata: json("metadata"),
	},
	(table) => ({
		indexes: [
			index("user_kyc_tenant_id_idx").on(table.tenantId), // Index for tenantId
			uniqueIndex("user_kyc_user_id_idx").on(table.userId),
		],
	}),
);

export const userKycRelations = relations(userKyc, ({ one }) => ({
	tenant: one(tenants, {
		fields: [userKyc.tenantId],
		references: [tenants.id],
	}),
	user: one(users, {
		fields: [userKyc.userId],
		references: [users.id],
	}),
}));
