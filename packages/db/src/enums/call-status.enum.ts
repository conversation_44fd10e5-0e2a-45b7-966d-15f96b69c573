import { pgEnum } from "drizzle-orm/pg-core";

const VALUES = [
	"SCHEDULED",
	"RINGING",
	"ANSWERED",
	"MISSED",
	"ENDED",
	"FAILED",
] as const;

export const callStatusDbEnum = pgEnum("calls_status_enum", VALUES);
export type CallStatusDb = (typeof callStatusDbEnum.enumValues)[number];

export enum CallStatus {
	SCHEDULED = "SCHEDULED",
	RINGING = "RINGING",
	ANSWERED = "ANSWERED",
	MISSED = "MISSED",
	ENDED = "ENDED",
	FAILED = "FAILED",
}
