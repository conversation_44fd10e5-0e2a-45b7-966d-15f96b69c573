// packages/db/src/enums/sms-status.enum.ts
import { pgEnum } from "drizzle-orm/pg-core";

export const smsStatusEnum = pgEnum("sms_status", [
	"PENDING", // Request to send received by our system
	"QUEUED", // Accepted by gateway, waiting to be sent
	"SENT", // Sent by gateway to carrier
	"FAILED", // Failed to send by gateway (initial attempt)
	"DELIVERED", // Delivered to recipient handset
	"UNDELIVERED", // Failed to deliver to handset (e.g., phone off, invalid number after gateway acceptance)
	"RECEIVED", // For inbound SMS
	"READ", // If read receipts are supported/tracked (optional)
]);