// src/commands/start-handlers.service.ts
import { Injectable, Logger } from "@nestjs/common";
// Import the correct context type
import type { AppBot, AppCommandContext } from "@/types/bot-context.js";

import { greetingScene } from "@/scenes/greeting.scene.js";

@Injectable()
export class StartCommandHandlersService {
	private readonly logger = new Logger(StartCommandHandlersService.name);
	private botInstance!: AppBot;

	public initialize(bot: AppBot): void {
		this.botInstance = bot;
	}

	registerHandlers(): void {
		this.botInstance.command("start", (ctx) =>
			this.handleStartCommand(ctx as AppCommandContext),
		);
		// Example for a command that might initiate a scene
		this.botInstance.command("greeting", (ctx: any) =>
			this.handleGreetingCommand(ctx as AppCommandContext),
		);
	}

	private async handleStartCommand(context: AppCommandContext): Promise<void> {
		try {
			await context.send(context.t("hi"));
		} catch (error) {
			this.logger.error("Failed to send start command response", error);
			// Fallback to simple text if translation fails
			await context.send("Hi!");
		}
	}

	private async handleGreetingCommand(
		context: AppCommandContext,
	): Promise<void> {
		// You can enter a scene from a command context
		await context.scene.enter(greetingScene);
	}
}
