// src/commands/callback-query-handlers.service.ts
import { Injectable, Logger } from "@nestjs/common";
import { CallbackData, InlineKeyboard } from "gramio";
import type {
	AppBot,
	AppCallbackQueryShorthandContext,
	AppCommandContext,
} from "@/types/bot-context.js";

const buttonData = new CallbackData("action").number("action_id");

@Injectable()
export class CallbackQueryHandlersService {
	private readonly logger = new Logger(CallbackQueryHandlersService.name);

	private botInstance!: AppBot;

	public initialize(bot: AppBot): void {
		this.botInstance = bot;
	}

	registerHandlers(): void {
		this.logger.log("Registering callback query handlers");

		// Use AppCommandContext for a command that sends a keyboard
		this.botInstance.command("start_action", (ctx: any) =>
			this.handleStartActionCommand(ctx as AppCommandContext),
		);

		this.botInstance.callbackQuery(buttonData, (ctx) => {
			const context = ctx as AppCallbackQueryShorthandContext<
				typeof buttonData
			>;
			if (!context.queryData?.action_id) {
				this.logger.warn("Received callback query without action_id");
				return;
			}
			return this.handleActionButtonCallback(context);
		});
	}

	async handleStartActionCommand(
		context: AppCommandContext, // Changed from AppMessageContextInScene
	): Promise<void> {
		this.logger.debug(
			`Handling start_action command from user ${context.from?.id}`,
		);

		await context.send(context.t?.("chooseAction") ?? "Choose an action:", {
			reply_markup: new InlineKeyboard().text(
				context.t("doAction1"),
				buttonData.pack({ action_id: 1 }),
			),
		});
	}

	async handleActionButtonCallback(
		// This type is correct as defined in bot-context.ts and extended by CallbackData
		context: AppCallbackQueryShorthandContext<typeof buttonData> & {
			queryData: { action_id: number };
		},
	): Promise<void> {
		this.logger.debug(
			`Handling action callback with ID: ${context.queryData.action_id} from user ${context.from?.id}`,
		);

		await context.send(
			context.t?.("selectedAction", context.queryData.action_id.toString()) ??
				`You selected action with ID: ${context.queryData.action_id}`,
		);

		await context.answer({
			text: String(
				context.t?.("actionReceived", context.queryData.action_id.toString()) ??
					`Action ${context.queryData.action_id} received!`,
			),
		});
	}
}
