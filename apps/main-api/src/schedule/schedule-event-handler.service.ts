import { Injectable, Logger } from '@nestjs/common';
import type { AckOrNackFn, Message, RascalService } from '@/rabbitmq/rascal.service';
import type { ScheduleCreateRequestedPayload, ScheduleCreatedPayload } from '@repo/types';
import { pbxCalls, type NewPbxCall } from '@repo/db';
import type { DatabaseService } from '@/services/database/database.service';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class ScheduleEventHandlerService {
  private readonly logger = new Logger(ScheduleEventHandlerService.name);

  constructor(
    private readonly rascalService: RascalService,
    private readonly dbService: DatabaseService,
  ) {
    this.subscribeToScheduleEvents();
  }

  private async subscribeToScheduleEvents() {
    await this.rascalService.subscribe(
      'sub_schedule_create_requested',
      {
        message: async (content: ScheduleCreateRequestedPayload, _message: Message, ackOrNack: AckOrNackFn) => {
          try {
            this.logger.log(`Consumed schedule.create.requested event: ${content.eventId}`);
            await this.handleScheduleCreateRequested(content);
            ackOrNack();
          } catch (error) {
            this.logger.error(`Error processing schedule.create.requested event ${content.eventId}:`, error);
            ackOrNack(error as Error, { strategy: 'nack', requeue: false });
          }
        },
      },
    );
  }

  private async handleScheduleCreateRequested(payload: ScheduleCreateRequestedPayload): Promise<void> {
    const { tenantId, data: scheduleDetails, actor } = payload;
    const db = this.dbService.getDb();

    const newScheduledCallData: NewPbxCall = {
      tenantId,
      userId: scheduleDetails.passengerUserId,
      direction: 'OUTBOUND',
      status: 'SCHEDULED',
      startedAt: new Date(scheduleDetails.scheduledTime),
      // Use pbxRawDetails or another JSONB field for metadata if 'metadata' is not a direct column
      // Assuming pbxRawDetails is the correct field for storing arbitrary JSON data
      pbxRawDetails: {
 type: 'scheduled',
 pickupAddress: scheduleDetails.pickupAddress,
 dropoffAddress: scheduleDetails.dropoffAddress,
 notes: scheduleDetails.notes,
 scheduledBy: actor,
      },
    };

    const [createdScheduledCall] = await db.insert(pbxCalls).values(newScheduledCallData).returning();

    this.logger.log(`Created scheduled PbxCall record: ${createdScheduledCall.id} for tenant ${tenantId}`);

    const scheduleCreatedEvent: ScheduleCreatedPayload = {
      eventId: uuidv4(),
      timestamp: new Date().toISOString(),
      tenantId,
      sourceService: 'ScheduleEventHandlerService',
      correlationId: payload.eventId,
      actor: payload.actor,
      data: { // Ensure this matches ScheduleCreatedPayload data structure
        ...createdScheduledCall,
      },
    };

    await this.rascalService.publish(
      'pub_schedule_event',
      scheduleCreatedEvent,
      { routingKey: `schedule.created.${tenantId}` },
    );
    this.logger.log(`Published schedule.created event: ${scheduleCreatedEvent.eventId}`);
  }
}
