// apps/main-api/src/schedule/schedule-command.service.ts (Conceptual)
import { Injectable, Logger } from '@nestjs/common';
// biome-ignore lint/style/useImportType: <explanation>
import { RascalService } from '@/rabbitmq/rascal.service'; // Your RabbitMQ service
import type { ScheduleCreateRequestedPayload, ScheduleDetails } from '@repo/types';
import { v4 as uuidv4 } from 'uuid'; // For generating eventId

@Injectable()
export class ScheduleCommandService {
  private readonly logger = new Logger(ScheduleCommandService.name);

  constructor(private readonly rascalService: RascalService) {}

  async requestScheduleCreation(
    tenantId: string,
    scheduleDetails: ScheduleDetails, // Matches the input for the REST endpoint
    requestingActor: ScheduleCreateRequestedPayload['actor'],
  ): Promise<void> {
    const eventId = uuidv4();
    const eventPayload: ScheduleCreateRequestedPayload = {
      eventId,
      timestamp: new Date().toISOString(),
      tenantId,
      sourceService: 'ScheduleCommandService',
      actor: requestingActor,
      data: scheduleDetails,
    };

    try {
      // Define your publication name in rascal-definitions.ts, e.g., "pub_schedule_event"
      // The routing key can be dynamic, e.g., "schedule.create.requested"
      await this.rascalService.publish(
        'pub_schedule_event', // Your Rascal publication name
        eventPayload,
        { routingKey: `schedule.create.requested.${tenantId}` }, // Example dynamic routing key
      );
      this.logger.log(`Published schedule.create.requested event: ${eventId} for tenant ${tenantId}`);
    } catch (error) {
      this.logger.error(`Failed to publish schedule.create.requested for tenant ${tenantId}`, error);
      // Handle error, maybe throw an exception to be caught by controller
      throw error;
    }
  }
}
