import {
	Injectable,
	Logger,
	type OnM<PERSON>uleD<PERSON>roy,
	type OnModuleInit,
} from "@nestjs/common";
import type { ConfigService } from "@nestjs/config";
import Rascal from "rascal";
import type { Message } from "amqplib";
import type { BrokerAsPromised, BrokerConfig, SubscriberSessionAsPromised } from "rascal";
import type { RascalConfig } from "@/rabbitmq/rascal-config.service";

// Re-export the AckOrNack type for use in other files
export type AckOrNack = (err?: Error, options?: { strategy?: "ack" | "nack" | "republish" | "forward" }) => void;

@Injectable()
export class RascalService implements OnModuleInit, OnModuleDestroy {
	private broker!: BrokerAsPromised;
    	private isCircuitOpen = false; // Re-added for health check
	private readonly logger = new Logger(RascalService.name);
	private readonly staticRascalConfig: BrokerConfig;

	constructor(
		_configService: ConfigService, // Mark as unused if needed for future, or remove
		private readonly rascalConfig: RascalConfig,
	) {
		this.staticRascalConfig = this.buildStaticConfig();
	}

	private buildStaticConfig(): BrokerConfig {
		return {
			vhosts: {
				"/": {
					connection: {
						options: {
							heartbeat: 10,
							connectionTimeout: 30000,
						},
						socketOptions: {
							timeout: 30000,
							...({
								noDelay: true,
								keepalive: true,
								keepaliveDelay: 60000,
							} as any),
						},
						retry: {
							min: 1000,
							max: 60000,
							factor: 2,
							strategy: "exponential",
						},
					},
					exchanges: {
						default: { type: "topic" },
						nestjs_example: { type: "topic" },
						calls: { type: "topic" },
						call_events_exchange: { type: "topic" },
						taxi_exchange: { type: "topic" },
						marketing_exchange: { type: "topic" },
						public_relations_exchange: { type: "topic" },
						dead_letter_exchange: { type: "topic" },
						drivers_exchange: { type: "topic" },
						application_events: { type: "topic" },
						telegram_exchange: { type: "topic" },
					},
					queues: {
						"call-created-queue": {
							options: {
								arguments: {
									"x-dead-letter-exchange": "dead_letter_exchange",
									"x-dead-letter-routing-key": "call.created.dead",
								},
							},
						},
						"call-created-dead-letter-queue": {
							options: {
								arguments: {
									"x-queue-type": "classic",
								},
							},
						},
						"test-tenant-queue": {
							options: {
								arguments: {
									"x-dead-letter-exchange": "dead_letter_exchange",
								},
							},
						},
						"driver-status-changed-queue": {
							options: {
								arguments: {
									"x-dead-letter-exchange": "dead_letter_exchange",
									"x-dead-letter-routing-key": "driver.status.changed.dead",
								},
							},
						},
						"driver-location-changed-queue": {
							options: {
								arguments: {
									"x-dead-letter-exchange": "dead_letter_exchange",
									"x-dead-letter-routing-key": "driver.location.changed.dead",
								},
							},
						},
						"notification-created-queue": {
							options: {
								arguments: {
									"x-dead-letter-exchange": "dead_letter_exchange",
									"x-dead-letter-routing-key": "notification.created.dead",
								},
							},
						},
						"taxi-company-created-queue": {
							options: {
								arguments: {
									"x-dead-letter-exchange": "dead_letter_exchange",
									"x-dead-letter-routing-key": "taxi.company.created.dead",
								},
							},
						},
						telegram_bot_queue: {
							options: {
								arguments: {
									"x-dead-letter-exchange": "dead_letter_exchange",
									"x-dead-letter-routing-key": "telegram.bot.message.dead",
								},
							},
						},
						"taxi-events-queue": {
							options: {
								arguments: {
									"x-dead-letter-exchange": "dead_letter_exchange",
									"x-dead-letter-routing-key": "taxi.events.dead",
								},
							},
						},
					},
					bindings: [
						"calls[call.created] -> call-created-queue",
						"dead_letter_exchange[call.created.dead] -> call-created-dead-letter-queue",
						"nestjs_example[test.tenant.#] -> test-tenant-queue",
						"drivers_exchange[driver.status.changed] -> driver-status-changed-queue",
						"drivers_exchange[driver.location.changed] -> driver-location-changed-queue",
						"application_events[notification.created] -> notification-created-queue",
						"taxi_exchange[taxi.company.created] -> taxi-company-created-queue",
						"telegram_exchange[telegram.bot.message] -> telegram_bot_queue",
						"taxi_exchange[taxi.events] -> taxi-events-queue",
					],
					publications: {
						"call-created": {
							exchange: "calls",
							routingKey: "call.created",
						},
						"test-tenant": {
							exchange: "nestjs_example",
							routingKey: "test.tenant.message",
						},
						"driver-status-changed": {
							exchange: "drivers_exchange",
							routingKey: "driver.status.changed",
						},
						"driver-location-changed": {
							exchange: "drivers_exchange",
							routingKey: "driver.location.changed",
						},
						"notification-created": {
							exchange: "application_events",
							routingKey: "notification.created",
						},
						"telegram-bot-message": {
							exchange: "telegram_exchange",
							routingKey: "telegram.bot.message",
						},
						"taxi-company-created": {
							exchange: "taxi_exchange",
							routingKey: "taxi.company.created",
						},
						"taxi-events": {
							exchange: "taxi_exchange",
							routingKey: "taxi.events",
						},
					},
					subscriptions: {
						"call-created-subscription": {
							queue: "call-created-queue",
						},
						"test-tenant-subscription": {
							queue: "test-tenant-queue",
						},
						"driver-status-changed-subscription": {
							queue: "driver-status-changed-queue",
						},
						"driver-location-changed-subscription": {
							queue: "driver-location-changed-queue",
						},
						"driver-location-updates": {
							queue: "driver-location-changed-queue",
						},
						"notification-created-subscription": {
							queue: "notification-created-queue",
						},
						"taxi-company-created-subscription": {
							queue: "taxi-company-created-queue",
						},
						telegram_bot_queue: {
							queue: "telegram_bot_queue",
						},
						"taxi-events-subscription": {
							queue: "taxi-events-queue",
						},
					},
				},
			},
		};
	}

	private buildRascalConfig(): BrokerConfig {
		return {
			...this.staticRascalConfig,
			vhosts: {
				"/": {
					...this.staticRascalConfig.vhosts?.["/"],
					connection: {
						...this.staticRascalConfig.vhosts?.["/"]?.connection,
						url: this.rascalConfig.url,
						hostname: this.rascalConfig.host,
						port: this.rascalConfig.port,
						user: this.rascalConfig.user,
						password: this.rascalConfig.password,
					},
				},
			},
		};
	}

	async onModuleInit() {
    		if (this.broker) {
    			this.logger.warn("Rascal broker is already initialized.");
    			return;
    		}
		try {
    			const config = this.buildRascalConfig();
    			this.broker = await Rascal.BrokerAsPromised.create(config);
    
    			this.broker.on("error", (err, { vhost, connectionUrl }) => {
    				this.logger.error(`Rascal Broker error for VHost: ${vhost} (${connectionUrl})`, err.stack);
    				this.isCircuitOpen = true; // Example: set circuit open on general broker error
    			});
    			this.broker.on("vhost_initialised", ({ vhost, connectionUrl }) => {
    				this.logger.log(`VHost: ${vhost} initialised with connection: ${connectionUrl}`);
    				this.isCircuitOpen = false;
    			});
    			this.broker.on("vhost_failed", ({ vhost, connectionUrl, err }) => {
    				this.logger.error(`VHost: ${vhost} failed to initialise with connection: ${connectionUrl}`, err.stack);
    				this.isCircuitOpen = true;
    			});
    			this.broker.on("connected", ({ vhost, connectionUrl }) => {
    				this.logger.log(`Successfully connected to RabbitMQ VHost: ${vhost} via ${connectionUrl}`);
    				this.isCircuitOpen = false;
    			});
    			this.broker.on("disconnected", ({ vhost, connectionUrl }) => {
    				this.logger.warn(`Disconnected from RabbitMQ VHost: ${vhost} via ${connectionUrl}. Attempting to reconnect...`);
    				this.isCircuitOpen = true;
    			});
    
    			this.logger.log("Rascal Broker initialized successfully and event listeners attached.");
		} catch (error) {
    			this.logger.error("Failed to initialize RabbitMQ Broker", error instanceof Error ? error.stack : error);
    			this.isCircuitOpen = true;
    			throw error;
		}
	}

	async onModuleDestroy() {
    		this.logger.log("Shutting down Rascal broker...");
		if (this.broker) {
			try {
				await this.broker.shutdown();
    				this.logger.log("Rascal broker shut down successfully.");
			} catch (error) {
    				this.logger.error("Error during RabbitMQ broker shutdown", error instanceof Error ? error.stack : error);
			}
		}
	}

	getRecoveryStrategy(error: any): "republish" | "nack" | "forward" {
		if (error.isRetriable) {
			return "republish";
		}
		return "nack";
	}
    
    	public getIsCircuitOpen(): boolean {
    		return this.isCircuitOpen || !this.broker || (this.broker as any)?.isVhostInitialized?.('/') === false;
    	}
    
    	public async publish(publicationName: string, message: any, options?: Record<string, any>): Promise<string> {
    		if (!this.broker) {
    			this.logger.error(`Cannot publish to ${publicationName}: Broker not initialized.`);
    			throw new Error("Rascal broker is not initialized.");
    		}
    		try {
    			const publication = await this.broker.publish(publicationName, message, options);
    			return new Promise((resolve, reject) => {
    				publication.on("success", resolve);
    				publication.on("error", reject);
    			});
    		} catch (error) {
    			this.logger.error(`Failed to publish to ${publicationName}`, error instanceof Error ? error.stack : error);
    			throw error;
    		}
    	}
    
    	public async subscribe(
    		subscriptionName: string,
    		eventHandlers: Record<string, (message: Message, content: any, ackOrNack: AckOrNack) => void | Promise<void>>,
    		options?: Record<string, any>,
    	): Promise<SubscriberSessionAsPromised> {
    		if (!this.broker) {
    			this.logger.error(`Cannot subscribe to ${subscriptionName}: Broker not initialized.`);
    			throw new Error("Rascal broker is not initialized.");
    		}
		const subscription = await this.broker.subscribe(subscriptionName, options);
		
		// Handle specific event types with proper type checking
		if (eventHandlers.message) {
			subscription.on('message', eventHandlers.message as any);
		}
		if (eventHandlers.error) {
			subscription.on('error', eventHandlers.error as any);
		}
		if (eventHandlers.cancelled) {
			subscription.on('cancelled', eventHandlers.cancelled as any);
		}
		if (eventHandlers.invalid_content) {
			subscription.on('invalid_content', eventHandlers.invalid_content as any);
		}
		if (eventHandlers.redeliveries_exceeded) {
			subscription.on('redeliveries_exceeded', eventHandlers.redeliveries_exceeded as any);
		}
		if (eventHandlers.redeliveries_error) {
			subscription.on('redeliveries_error', eventHandlers.redeliveries_error as any);
		}
		
		return subscription;
	}
}
