// In CallEventHandlerService
import type { AckOrNackFn, RascalService } from '@/rabbitmq/rascal.service';
// import type { PbxCallIncomingPayload } from '@repo/types'; // Assuming this will be resolved by your monorepo setup
import { Injectable, Logger, type OnModuleInit } from '@nestjs/common';
import type { Message } from 'amqplib';

// Placeholder type if @repo/types is not yet resolved
interface PbxCallIncomingPayload {
  tenantId: string;
  data: {
    fromPhoneNumber: string;
    // other properties
  };
}

@Injectable()
export class CallEventHandlerService implements OnModuleInit {
  private readonly logger = new Logger(CallEventHandlerService.name);

  constructor(private readonly rascalService: RascalService) {}
  async onModuleInit() {
    await this.rascalService.subscribe(
      'sub_process_new_call', // Matches subscription name in Rascal config
      {
        message: async (content: PbxCallIncomingPayload, _message: Message, ackOrNack: AckOrNackFn) => {
          try {
            const { tenantId, data } = content; // Destructure from the typed content
            this.logger.log(`Processing pbx.call.incoming for tenant ${tenantId}: ${data.fromPhoneNumber}`);
            // ... your logic to create a PbxCall record in DB ...
            // const newCall = await this.dbService.createCall(tenantId, data);
            // await this.rascalService.publish('pub_call_event', { eventId: uuid(), ..., data: newCall }, { routingKeyCtx: { eventType: 'call.created', tenantId } }); // Acknowledge successful processing
 ackOrNack();
          } catch (error) {
            this.logger.error('Error processing incoming call event:', error);
            const nackError = error instanceof Error ? error : new Error(String(error));
            ackOrNack(nackError, [{ strategy: 'nack', requeue: true }]); // Corrected NACK strategy
          }
        },
      },
    );
  }
}
