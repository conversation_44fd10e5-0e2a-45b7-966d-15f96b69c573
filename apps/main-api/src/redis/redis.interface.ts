import type { Redis } from "ioredis";

export interface IRedisService {
	get(tenantId: string, key: string): Promise<string | null>;
	set(
		tenantId: string,
		key: string,
		value: string | number,
		ttl?: number,
	): Promise<void>;
	del(tenantId: string, key: string): Promise<void>;
	publish(channel: string, message: string): Promise<number>;
	subscribe(
		channel: string,
		callback: (channel: string, message: string) => unknown,
	): Promise<void>;
	unsubscribe(channel: string): Promise<void>;
	keys(tenantId: string, pattern: string): Promise<string[]>;
	exists(tenantId: string, key: string): Promise<boolean>;
	expire(tenantId: string, key: string, seconds: number): Promise<number>;
	ttl(tenantId: string, key: string): Promise<number>;
	incr(tenantId: string, key: string): Promise<number>;
	decr(tenantId: string, key: string): Promise<number>;
	checkHealth(): Promise<{ status: string; error?: string }>;
	getCacheClient(): Redis; // Changed Redis.Redis to Redis
}
