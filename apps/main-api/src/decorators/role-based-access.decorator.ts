import { Logger } from "@nestjs/common";
import type { AppBaseContext } from "@/types/bot-context.js"; // Using a more specific context

const logger = new Logger("RoleBasedAccessDecorator");

/**
 * Decorator for role-based access control in command handlers
 * @param requiredRoles Array of roles required to execute the command
 * @returns Decorator function that checks if the user has the required roles
 */
export function RequireRoles(requiredRoles: string[]) {
	return (
		_target: any,
		_propertyKey: string,
		descriptor: PropertyDescriptor,
	) => {
		// Store the original method
		const originalMethod = descriptor.value;

		// Replace the original method with a new one that checks roles
		descriptor.value = async function (
			ctx: AppBaseContext & {
				from?: { id?: number };
				chat?: { id?: number };
				tenantBotApiClient?: any;
				userRoles?: string[];
				t?: (...args: any[]) => string;
			}, // Use a context that has 'from', 'chat', 't', and 'tenantBotApiClient'
			...args: any[]
		) {
			// Get user roles from context (added by derive function)
			const userRoles = ctx.userRoles ?? [];

			// Check if the user has any of the required roles
			const hasRequiredRole = requiredRoles.some((role) =>
				userRoles.includes(role),
			);

			if (hasRequiredRole) {
				// User has at least one of the required roles, proceed with the original method
				return originalMethod.apply(this, [ctx, ...args]);
			}
			// User doesn't have any of the required roles, send access denied message
			logger.warn(
				`Access denied for user ${ctx.from?.id} - Required roles: ${requiredRoles.join(", ")}, User roles: ${userRoles.join(", ")}`,
			);

			// Send access denied message if the context has a chat ID and tenantBotApiClient
			if (ctx.chat?.id && ctx.tenantBotApiClient) {
				try {
					await ctx.tenantBotApiClient.sendMessage({
						chat_id: ctx.chat.id,
						text: "Access denied. You do not have permission to use this command.",
					});
				} catch (e) {
					logger.error(
						"Failed to send access denied message from decorator",
						e,
					);
				}
			}
			// Return without calling the original method
			return;
		};

		return descriptor;
	};
}

/**
 * Decorator for admin-only access in command handlers
 * @returns Decorator function that checks if the user has the admin role
 */
export function AdminOnly() {
	return RequireRoles(["admin"]);
}

/**
 * Decorator for moderator access in command handlers
 * @returns Decorator function that checks if the user has the moderator or admin role
 */
export function ModeratorOnly() {
	return RequireRoles(["admin", "moderator"]);
}

/**
 * Decorator for support access in command handlers
 * @returns Decorator function that checks if the user has the support, moderator, or admin role
 */
export function SupportOnly() {
	return RequireRoles(["admin", "moderator", "support"]);
}
