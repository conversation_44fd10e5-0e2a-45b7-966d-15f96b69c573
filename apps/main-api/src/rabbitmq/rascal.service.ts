// apps/main-api/src/rabbitmq/rascal.service.ts
import { Injectable, Logger, type OnModuleInit, type OnModuleDestroy } from '@nestjs/common';
import type { ConfigService } from '@nestjs/config'; // For accessing Rascal config
import { BrokerAsPromised, type BrokerConfig, type PublicationSession, type SubscriberSessionAsPromised, type Recovery } from 'rascal'; // Import Recovery
// import type { AppConfigService } from '@/config/app-config.service'; // Your app config service - marked as unused
import { rascalDefinitions } from '@/config/rascal-definitions'; // Import the definitions
import type { Message } from 'amqplib'; export type { Message }; // Export Message

// Remove local RascalRecoveryOptions and RascalRecovery as they are incompatible
// with the 'rascal' library's 'Recovery' type.
// export interface RascalRecoveryOptions { ... }
// export interface RascalRecovery { ... }

export type AckOrNackFn = (err?: Error, recovery?: Recovery | Recovery[]) => void; // Use Recovery from rascal

export interface SubscriptionEventHandlers {
  message: (content: any, message: Message, ackOrNack: AckOrNackFn) => Promise<void>;
  error?: (error: Error) => void;
  cancelled?: (err?: Error | undefined) => void;
  invalid_content?: (err: Error, message: Message, ackOrNack: AckOrNackFn) => void;
  redeliveries_exceeded?: (err: Error, message: Message, ackOrNack: AckOrNackFn) => void;
}
@Injectable()
export class RascalService implements OnModuleInit, OnModuleDestroy {
  private broker: BrokerAsPromised | null = null;
  private readonly logger = new Logger(RascalService.name);
  private isInitialized = false;
  constructor(private readonly nestConfigService: ConfigService) {} // Standard NestJS ConfigService, appConfigService removed as unused

  async onModuleInit() {
    if (this.isInitialized) return;
    try {
      // Use the imported Rascal definitions
      const rascalBrokerConfig: BrokerConfig = JSON.parse(JSON.stringify(rascalDefinitions)); // Deep clone to avoid mutation

 if (rascalBrokerConfig.vhosts?.['/'] && !rascalBrokerConfig.vhosts['/'].connection?.url) {
 rascalBrokerConfig.vhosts['/'].connection = {
 ...rascalBrokerConfig.vhosts['/'].connection,
 url: this.nestConfigService.get<string>('RABBITMQ_URL'),
         };
      }

      this.broker = await BrokerAsPromised.create(rascalBrokerConfig);
      this.broker.on('error', (err) => this.logger.error('Rascal Broker error:', err));
      this.broker.on('vhost_initialised', ({ vhost }) => this.logger.log(`Rascal VHost ${vhost} initialised.`));
      this.logger.log('Rascal Broker connected and initialized successfully.');
      this.isInitialized = true;
    } catch (err) {
      this.logger.error('Failed to initialize Rascal Broker:', err);
      // Implement retry logic or handle failure appropriately
    }
  }

  async onModuleDestroy() {
    if (this.broker) {
      await this.broker.shutdown();
      this.logger.log('Rascal Broker shutdown.');
    }
  }

  private ensureBroker(): BrokerAsPromised {
    if (!this.broker || !this.isInitialized) {
      this.logger.error('Rascal Broker is not initialized. Call onModuleInit or check initialization logs.');
      throw new Error('Rascal Broker not initialized.');
    }
    return this.broker;
  }

  /**
   * Publishes a message to a pre-defined publication in Rascal config.
   * @param publicationName The name of the publication (e.g., "pub_call_event").
   * @param message The message payload.
   * @param options Rascal publication options, can include `routingKeyCtx` for dynamic routing keys.
   *                Example: { routingKeyCtx: { tenantId: 'xyz', eventType: 'call.created' } }
   */
  async publish(
    publicationName: string,
    message: any,
    options?: Record<string, any> & { routingKeyCtx?: Record<string, string> },
  ): Promise<PublicationSession> {
    const broker = this.ensureBroker();
    const effectiveOptions = { ...options }; // Clone options

    // Ensure tenantId is in the message or headers for logging/processing
    // The actual routing key generation is handled by Rascal based on `routingKeyCtx`
    // and the function defined in the publication's `routingKey` config.
    if (options?.routingKeyCtx?.tenantId && typeof message === 'object' && !message.tenantId) {
        message.tenantId = options.routingKeyCtx.tenantId;
    }

    this.logger.debug(`Publishing to [${publicationName}] with options: ${JSON.stringify(options)}, Msg: ${JSON.stringify(message)}`);
    // Cast options to `any` to allow `routingKeyCtx` for Rascal's dynamic routing key functions
    return broker.publish(publicationName, message, effectiveOptions as any);
  }

  /**
   * Subscribes to a pre-defined subscription in Rascal config.
   * @param subscriptionName The name of the subscription (e.g., "sub_process_new_call").
   * @param handlers An object containing event handlers (message, error, cancelled, etc.).
   * @param options Optional Rascal subscription configuration overrides.
   */
  async subscribe(
    subscriptionName: string,
    handlers: SubscriptionEventHandlers,
    options?: Record<string, any>,
  ): Promise<SubscriberSessionAsPromised> { // Return SubscriptionSession for more control if needed
    const broker = this.ensureBroker();
    // The 'options' here are Rascal subscription options, not the event handlers
    const subscription = await broker.subscribe(subscriptionName, options);

    subscription.on('message', async (amqpMessage: Message, content: any, ackOrNackCallback: AckOrNackFn) => {
      const tenantId = amqpMessage.properties?.headers?.tenantId || content?.tenantId || 'unknown_tenant';
      this.logger.debug(`Received message on [${subscriptionName}] for tenant [${tenantId}]: ${JSON.stringify(content)}`);
      try {
        await handlers.message(content, amqpMessage, ackOrNackCallback);
      } catch (error) {
        this.logger.error(`Error processing message from [${subscriptionName}] for tenant [${tenantId}]:`, error);
        const nackError = error instanceof Error ? error : new Error(String(error));
        ackOrNackCallback(nackError, [{ strategy: 'nack', requeue: true }]);
      }
    });

    if (handlers.error) {
      subscription.on('error', handlers.error);
    } else {
      subscription.on('error', (err) => {
        this.logger.error(`Subscription error on [${subscriptionName}]:`, err.stack || err);
      });
    }

    if (handlers.cancelled) {
      subscription.on('cancelled', handlers.cancelled);
    } else {
      subscription.on('cancelled', (err) => {
        this.logger.warn(`Subscription [${subscriptionName}] was cancelled:`, err ? err.message : 'No error details', err?.stack);
      });
    }

    if (handlers.invalid_content) subscription.on('invalid_content', handlers.invalid_content as any);
    if (handlers.redeliveries_exceeded) subscription.on('redeliveries_exceeded', handlers.redeliveries_exceeded as any);

    this.logger.log(`Subscribed to [${subscriptionName}]`);
    return subscription;
  }
}