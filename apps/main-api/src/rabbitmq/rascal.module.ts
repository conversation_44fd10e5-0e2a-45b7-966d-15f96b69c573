// apps/main-api/src/rabbitmq/rascal.module.ts (rename your rabbit-m-q.module.ts)
import { Global, Module } from '@nestjs/common';
import { ConfigModule } from '@/config/config.module';
import { RascalService } from './rascal.service';

@Global() // Make RascalService available globally
@Module({
  imports: [
    ConfigModule, 
  ],
  providers: [RascalService],
  exports: [RascalService],
})
export class RascalAppModule {} // Renamed for clarity