import { Injectable } from "@nestjs/common";
import type { ConfigService } from "@nestjs/config";

@Injectable()
export class RascalConfig {
  constructor(private configService: ConfigService) {}

  get host(): string | undefined {
    return this.configService.get<string>('RABBITMQ_HOST');
  }

  get port(): number | undefined {
    return this.configService.get<number>('RABBITMQ_PORT');
  }

  get user(): string | undefined {
    return this.configService.get<string>('RABBITMQ_USER');
  }

  get password(): string | undefined {
    return this.configService.get<string>('RABBITMQ_PASSWORD');
  }

  get url(): string | undefined {
    return this.configService.get<string>('RABBITMQ_URL');
  }
}
