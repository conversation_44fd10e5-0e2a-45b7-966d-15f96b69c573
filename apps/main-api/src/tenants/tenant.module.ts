import { Module } from "@nestjs/common";
import { RedisModule } from "@/redis/redis.module.js";
import { TenantBotFactoryService } from "./tenant-bot-factory.service.js";
import { TenantRoleService } from "./tenant-role.service.js";

@Module({
	imports: [RedisModule],
	providers: [TenantBotFactoryService, TenantRoleService],
	exports: [TenantBotFactoryService, TenantRoleService],
})
export class TenantModule {}
