// apps/main-api/src/config/rascal-definitions.ts
import type { BrokerConfig } from 'rascal';

export const rascalDefinitions: BrokerConfig = {
  /**
   * Virtual hosts (vhosts) provide a way to segregate applications using the same RabbitMQ instance.
   * Each vhost has its own set of exchanges, queues, and bindings.
   */
  vhosts: {
    "/": { // Default vhost
      /**
       * Connection details for this vhost.
       * The URL is typically injected from environment variables for security and flexibility.
       */
      connection: {
        // URL will be injected by AppConfigService from environment variables
        // Example: "amqp://user:password@localhost:5672"
        // Ensure your AppConfigService provides this
      },
      /**
       * Exchanges are message routing agents, defined by name, type, and other properties.
       * - topic: Routes messages to queues based on wildcard matches between the routing key and binding key.
       * - direct: Routes messages to queues where the binding key exactly matches the routing key.
       * - fanout: Routes messages to all bound queues, ignoring the routing key.
       */
      exchanges: {
        // Domain-specific exchanges (Topic exchanges are very flexible)
        "tenant_events": { assert: true, type: "topic" },
        "call_events": { assert: true, type: "topic" },
        "schedule_events": { assert: true, type: "topic" },
        "messaging_events": { assert: true, type: "topic" },
        "pbx_events": { assert: true, type: "topic" },
        "chatbot_events": { assert: true, type: "topic" },
        "notification_events": { assert: true, type: "topic" },
        /**
         * Dead Letter Exchange (DLX) for messages that cannot be processed successfully.
         * Messages are sent here by queues configured with a DLX.
         */
        "dead_letter_exchange": { assert: true, type: "fanout" },
      },
      /**
       * Queues store messages until they are consumed.
       * - assert: true ensures the queue is created if it doesn't exist.
       * - durable: true means the queue will survive a broker restart.
       * - deadLetterExchange: Specifies the DLX to send messages to if they are rejected or expire.
       */
      queues: {
        /**
         * Queue for processing new incoming call events from the PBX.
         * This is a common pattern for decoupling initial event reception from processing logic.
         */
        "process_new_call_q": {
          assert: true,
          options: {
            durable: true,
            deadLetterExchange: "dead_letter_exchange", // Send failed messages here
          },
        },
        /**
         * Queue for handling requests to send SMS messages.
         * Allows for asynchronous SMS sending and retry mechanisms.
         */
        "send_sms_q": {
          assert: true,
          options: { durable: true, deadLetterExchange: "dead_letter_exchange" },
        },
        /**
         * Queue for messages intended to be pushed to client dashboards via WebSockets.
         * Consumed by a service that manages WebSocket connections.
         * Durability might be false if updates are transient and missed updates are acceptable or re-fetched.
         */
        "dashboard_push_updates_q": {
          assert: true,
          options: { durable: false, deadLetterExchange: "dead_letter_exchange" }, // May not need to be durable
        },
        /**
         * Dead Letter Queue (DLQ) stores messages from the dead_letter_exchange.
         * These messages can be inspected for debugging or reprocessed manually/automatically.
         */
        "dead_letter_queue": {
          assert: true,
          options: { durable: true },
        },
        // Tenant-specific queues (dynamic creation or pattern-based)
        // For highly isolated processing, you might have queues per tenant for certain critical events.
        // Rascal can handle dynamic queue assertion if needed, or you can use routing keys.
        // Example: "tenant_notifications_q_tenant_abc": { ... }
      },
      /**
       * Bindings define how messages are routed from exchanges to queues (or other exchanges).
       * - source: The name of the exchange.
       * - destination: The name of the queue or exchange.
       * - bindingKey: A pattern used by the exchange to route messages (depends on exchange type).
       */
      bindings: {
        // Route pbx.call.incoming events to the process_new_call_q
        "pbx_call_incoming_to_process_new_call": {
          source: "pbx_events",
          destination: "process_new_call_q",
          destinationType: "queue",
          bindingKey: "pbx.call.incoming.#", // Catches all incoming PBX call events (e.g., pbx.call.incoming.tenant123)
        },
        // Route call.created events (after initial processing) to dashboard push queue
        "call_created_to_dashboard_push": {
          source: "call_events",
          destination: "dashboard_push_updates_q",
          destinationType: "queue",
          bindingKey: "call.created.*", // tenantId can be part of routing key
        },
        "call_updated_to_dashboard_push": {
          source: "call_events",
          destination: "dashboard_push_updates_q",
          destinationType: "queue",
          bindingKey: "call.updated.*",
        },
        // Route sms.send.request to the send_sms_q
        "sms_send_request_to_send_sms": {
          source: "messaging_events",
          destination: "send_sms_q",
          destinationType: "queue",
          bindingKey: "sms.send.request",
        },
        // Bind DLX to dead_letter_queue
        /**
         * Binds the dead_letter_exchange to the dead_letter_queue.
         * Since dead_letter_exchange is a fanout, no bindingKey is needed; all messages go to bound queues.
         */
        "dlx_to_dlq": {
          source: "dead_letter_exchange",
          destination: "dead_letter_queue",
          destinationType: "queue",
        },
      },
      /**
       * Publications define named configurations for publishing messages.
       * - exchange: The target exchange for messages.
       * - routingKey: Can be a static string or a function that dynamically generates the routing key based on context.
       */
      publications: {
        // Publish to tenant_events exchange
        "pub_tenant_event": {
          exchange: "tenant_events",
          routingKey: ((ctx: { eventType: string; tenantId?: string }) => `${ctx.tenantId ? `tenant.${ctx.tenantId}` : 'system'}.${ctx.eventType}`) as unknown as string,
        },
        // Publish to call_events exchange
        /**
         * Publication for call-related events.
         * Routing key includes eventType, tenantId, and optionally callId for granular routing.
         * Example routing key: "call.created.tenant123.callABC"
         */
        "pub_call_event": {
          exchange: "call_events",
          routingKey: ((ctx: { eventType: string; tenantId: string; callId?: string }) => `${ctx.eventType}.${ctx.tenantId}${ctx.callId ? `.${ctx.callId}` : ''}`) as unknown as string,
        },
        /**
         * Publication for raw PBX events.
         * Routing key includes eventType and optionally tenantId.
         * Example routing key: "pbx.call.incoming.tenant123" or "pbx.call.terminated.global"
         */
        "pub_pbx_event": {
          exchange: "pbx_events",
          routingKey: ((ctx: { eventType: string; tenantId?: string }) => `${ctx.eventType}${ctx.tenantId ? `.${ctx.tenantId}` : '.global'}`) as unknown as string,
        },
        /**
         * Publication for messaging-related events (e.g., SMS, chat).
         * Routing key includes eventType and tenantId.
         * Example routing key: "sms.send.request.tenant123"
         */
        "pub_messaging_event": {
          exchange: "messaging_events",
          routingKey: ((ctx: { eventType: string; tenantId: string }) => `${ctx.eventType}.${ctx.tenantId}`) as unknown as string,
        },
        /**
         * Publication for notification events.
         * Routing key includes eventType, tenantId, and optionally userId for targeted notifications.
         * Example routing key: "user.notification.tenant123.userXYZ"
         */
        "pub_notification_event": {
 exchange: "notification_events",
          routingKey: ((ctx: { eventType: string; userId?: string; tenantId: string }) => `${ctx.eventType}.${ctx.tenantId}${ctx.userId ? `.${ctx.userId}` : ''}`) as unknown as string,
        },
      },
      /**
       * Subscriptions define named configurations for consuming messages from queues.
       * - queue: The queue to consume from.
       * - contentType: Expected message content type (e.g., "application/json").
       * - prefetch: The maximum number of unacknowledged messages a consumer can have (flow control).
       * - retry: Configuration for message redelivery attempts on failure.
       */
      subscriptions: {
        // Consume from process_new_call_q
        "sub_process_new_call": {
          queue: "process_new_call_q",
          contentType: "application/json",
        },
        "sub_send_sms": {
          queue: "send_sms_q",
          contentType: "application/json",
          prefetch: 5, // Process up to 5 SMS requests concurrently by this worker instance
        },
        "sub_dashboard_push_updates": {
          queue: "dashboard_push_updates_q",
          contentType: "application/json",
        }
      },
    },
  },
};