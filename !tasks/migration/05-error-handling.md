# Error Handling Strategy

## WebSocket Errors

The primary way the server communicates operational errors related to a client's WebSocket request is via the `server:error` event.

**Example `server:error` Payload (Unicast to originating client):**

```json
{
  "event": "server:error",
  "data": {
    "event": "call:edit", // The original event that caused the error
    "data": "call_id_123",  // Data from the original request, if applicable
    "msg": "This call is already locked" // Descriptive error message
  }
}
```

Another example for a failed SMS send:

```json
{
  "event": "server:error",
  "data": {
    "event": "message:new",
    "key": "client_temp_id_for_sms", // Helps client identify the failed message
    "msg": "SMS Gateway Timeout"
  }
}
```

Clients should listen for this event and handle errors appropriately, such as:

- Displaying a notification/toast to the user.
- Reverting optimistic UI updates.
- Logging the error for debugging.

The server also uses the `client:error` event name if it receives a malformed WebSocket message from the client (e.g., invalid JSON, missing `event` field).

## HTTP Exception Hierarchy (Conceptual for Rewrite)

For the HTTP API endpoints, a standard HTTP exception hierarchy should be implemented in the new backend.

**Base Exception:**

```typescript
// Example for a new Node.js/TypeScript backend
class HttpException extends Error {
  constructor(
    public readonly statusCode: number,
    public readonly message: string,
    public readonly details?: any
  ) {
    super(message);
  }
}
```

**Specific Exceptions:**

```typescript
class NotFoundException extends HttpException {
  constructor(resource: string = 'Resource') {
    super(404, `${resource} not found`);
  }
}

class ValidationException extends HttpException {
  constructor(errors: any) {
    super(400, 'Validation failed', errors);
  }
}

class UnauthorizedException extends HttpException {
  constructor(message: string = 'Unauthorized') {
    super(401, message);
  }
}

class ForbiddenException extends HttpException {
  constructor(message: string = 'Forbidden') {
    super(403, message);
  }
}

class ConflictException extends HttpException { // e.g., for call already locked
  constructor(message: string = 'Conflict') {
    super(409, message);
  }
}
```

A global exception filter in the backend framework (e.g., NestJS, Express middleware) would catch these exceptions and format a consistent JSON error response:

```json
// Example HTTP Error Response
{
  "statusCode": 404,
  "message": "Operator not found",
  "timestamp": "2023-10-27T10:00:00.000Z",
  "path": "/api/operators/999"
}
```

```json
// Example HTTP Validation Error Response
{
  "statusCode": 400,
  "message": "Validation failed",
  "errors": [
    { "field": "phone", "message": "Phone number must be a valid format" }
  ],
  "timestamp": "2023-10-27T10:01:00.000Z",
  "path": "/api/calls"
}
