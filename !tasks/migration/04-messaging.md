# Messaging Features

## Messaging - Send SMS

**Functional Description:**
Allows an operator to send an SMS message to a specified phone number, optionally linking it to an existing call.

### API Interface

- **Protocol:** WebSocket
- **Event/Endpoint:** `message:new`
- **Request:**

  ```json
  {
    "event": "message:new",
    "data": {
      "temp_id": "string (client-generated temporary ID)",
      "type": "string ('sms')",
      "msg": "string (SMS content)",
      "phone": "string (recipient phone number)",
      "call_id": "string (optional, links SMS to a call)"
    }
  }
  ```

- **Response (Unicast to self, on success):**

  ```json
  {
    "event": "message:sent",
    "data": "string (temp_id)"
  }
  ```

- **Response (Unicast to self, on failure):**

  ```json
  {
    "event": "server:error",
    "data": {
      "event": "message:new",
      "key": "string (temp_id)",
      "msg": "string (error description from SMS gateway or system)"
    }
  }
  ```

- **Response (Broadcast to others, on success):**

  ```json
  {
    "event": "message:new",
    "data": {
      "operator_id": "number (sender's operator_id)",
      "type": "system",
      "from": "SMS ➜ <phone_number_sent_to>",
      "timestamp": "string (epoch ms)",
      "autohide": "number (configured duration)",
      "msg": [["<sms_content>", null]]
    }
  }
  ```

- **Authentication:** WebSocket (Operator Session)

### Business Rules

- Backend (`eventHandlers.js`) receives the request.
- Calls `asteriskGateway.sendSMS(phone, message, callback)`.
  - The gateway makes an HTTP request to a configured SMS gateway URL (from `asteriskGW.json`) with `number` and `msg` parameters.
  - It handles basic auth if configured for the gateway.
  - SMS text might be truncated if it exceeds 160 characters.
- If `sendSMS` is successful:
  - Server sends `message:sent` back to the originating operator with the `temp_id`.
  - Server broadcasts a system-type `message:new` to other operators, indicating an SMS was sent.
- If `sendSMS` fails:
  - Server sends `server:error` back to the originating operator with the `temp_id` and error message.
- Client-side (`MessagesService`):
  - Sets a `sending` flag on the call's metadata when an SMS is initiated.
  - Clears the `sending` flag upon receiving `message:sent` or `server:error`.
  - Uses `temp_id` to correlate responses with requests.

### Data Model

```mermaid
erDiagram
  CALLS {
    uuid id PK
    jsonb metadata "{ \"sending_sms\": boolean }"
  }
  SMS_GATEWAY_CONFIG {
    string hostname
    int port
    string smsPath
    string username "Nullable"
    string password "Nullable"
  }
  CALLS .. SMS_GATEWAY_CONFIG : "uses_for_sending (indirectly)"
```

- SMS content itself is not stored persistently in the database by this flow.
- `asteriskGW.json` contains SMS gateway configuration.

### Error Handling

- SMS gateway timeout.
- HTTP errors from SMS gateway (e.g., 4xx, 5xx).
- Invalid phone number format (handled by gateway).
- `server:error` event sent to client on failure.

---

## Messaging - Receive Incoming Messages/Notifications

**Functional Description:**
Client application receives various types of messages and notifications pushed by the server, including incoming SMS, system messages, and app-specific (Texy/TaxiPhone/Bookmark) notifications.

### API Interface

- **Protocol:** WebSocket (Server Push)
- **Event/Endpoint:** `message:new`
- **Request:** N/A (Server-initiated)
- **Response (Broadcast to clients):**
  - **SMS Type:**

    ```json
    {
      "event": "message:new",
      "data": {
        "id": "string (message_id, often uuid)",
        "type": "sms",
        "timestamp": "number (epoch ms)",
        "phone": "string (sender's phone)",
        "autohide": "number (duration in seconds for toast)",
        "msg": "string (SMS content)",
        "userInfo": { /* Contact info for sender, if available */
          "name": "string (optional)",
          "served": "number (optional)"
        },
        "callInfo": { /* Details of last active call with this contact, if available */
          "id": "string (optional)",
          "location": "string (optional)",
          "destination": "string (optional)",
          "metadata": { "vehicles": "Array<string> (optional)" }
        }
      }
    }
    ```

  - **System Type:**

    ```json
    {
      "event": "message:new",
      "data": {
        "operator_id": "number (optional, if from a specific operator)",
        "type": "system",
        "timestamp": "number (epoch ms)",
        "from": "string (source description, e.g., 'SMS ➜ <phone>')",
        "autohide": "number (duration in seconds)",
        "msg": [["translation_key_or_text", { "param": "value" } ], /* ...more message parts */ ]
      }
    }
    ```

  - **Texy/App Type (TaxiPhone, Bookmark, Restaurant, Courier):**

    ```json
    {
      "event": "message:new",
      "data": {
        "type": "string ('texy', 'taxiphone', 'bookmark', 'restaurant', 'courier')",
        "timestamp": "number (epoch ms)",
        "phone": "string",
        "callInfo": {
          "id": "string (uuid, often pre-generated)",
          "location": "string (optional)",
          "destination": "string (optional)",
          "area": "string (optional)",
          "metadata": { /* e.g., vehicles if pre-assigned by app */ }
        },
        "autohide": "number (duration in seconds, e.g., 99 for long)",
        "msg": "string (often location details)"
      }
    }
    ```

  - **Toast Type (Info, Success, Error):**

    ```json
    {
      "event": "message:new",
      "data": {
        "type": "string ('info', 'success', 'error')",
        "msg": "string (message body)",
        "from": "string (optional, title/source)",
        "autohide": "number (duration in seconds)"
      }
    }
    ```

- **Authentication:** WebSocket (Operator Session or Anonymous, depending on message type)

### Business Rules

- **Incoming SMS:**
  - Received by backend via `tcpListener.js` (legacy) or potentially a direct gateway integration.
  - `contacts.getDetails` is called to enrich with sender's info and last call details.
  - Broadcasted as `message:new` with `type: 'sms'`.
- **System Messages:**
  - Generated by various backend actions (e.g., after an operator sends an SMS, a system message is broadcast to others).
  - `msg` is an array allowing for structured, translatable messages.
- **Texy/App Notifications:**
  - Received by backend (e.g., `tcpListener.js` for 'bookmark', 'taxiphone'; `taxiphone.js` for 'mk.centkom.texy.merr' app events).
  - Contain call-like information.
  - Client-side, these notifications can be "answered" by an operator, triggering a `call:answer` event to the server.
- **Toast Messages:**
  - Generic informational, success, or error messages generated by the server.
- Client-side (`NotificationService`):
  - Displays these messages as toasts using `ngx-toastr`.
  - Toasts for SMS and Texy/App types can have action buttons (Add Call, Dial, Send SMS, Mark Unserved) if the operator is authenticated and SMS is enabled.

### Data Model

- No direct DB persistence for these messages themselves, but they often relate to `CALLS` or `CONTACTS_CACHE_REDIS`.

### Error Handling

- Malformed messages received by the backend might be logged or ignored.

```

---
**`docs/statistics.md`**
```markdown
# Statistics Features

## Statistics - Fetch Daily Statistics
**Functional Description:**
Retrieves aggregated call statistics (answered, unanswered, served, etc.) for a specified date range, typically displayed in charts and tables.

### API Interface
- **Protocol:** WebSocket
- **Event/Endpoint:** `stats:daily`
- **Request:**
  ```json
  {
    "event": "stats:daily",
    "data": {
      "from": { "year": "number", "month": "number", "date": "number" },
      "to": { "year": "number", "month": "number", "date": "number" }
    }
  }
  ```

- **Response (Unicast to self):**

  ```json
  {
    "event": "stats:daily",
    "data": {
      "YYYY-MM-DD": { // Key is the date string
        "answered": "string (count)", // Note: counts are strings from DB
        "unanswered": "string (count)",
        "served": "string (count)",
        "unserved": "string (count)",
        "canceled": "string (count)",
        "manual": "string (count)",
        "servedByVehicle": "string (JSON array of [vehicle_number, count] pairs, optional)"
      }
      // ... more dates
    }
  }
  ```

- **Authentication:** WebSocket (Operator Session)

### Business Rules

- Backend (`stats.js`) processes the date range.
- For each day in the range:
  - It calls `getDayStats(date, callStatuses, null, callback)`.
  - `getDayStats` checks if it's today's date:
    - If today: Calls `getDayStatsFromDB` directly (no caching for current day).
    - If past day: Calls `getDayStatsFromRedis`.
      - `getDayStatsFromRedis` checks if main day key `stats:YYYY:MM:DD` exists in Redis.
        - If exists: Fetches all HASH fields for that key.
        - If not exists: Calls `getDayStatsFromDB` to populate from PostgreSQL and then caches in Redis. It also queues a background task (`queueOperatorStats`) to load per-operator stats for this day into Redis.
  - `getDayStatsFromDB` queries the `calls` PostgreSQL table using `core.getCallCount` for each status type (`answered`, `unanswered`, `served`, `unserved`, `canceled`, `manual`, `servedByVehicle`).
    - `answered`: `timeanswered IS NOT NULL` AND `source != 'manual'`.
    - `unanswered`: `timeanswered IS NULL`.
    - `served`: `booking = 'served'` (vehicles exist, timeassigned exists).
    - `unserved`: `booking = 'unserved'` (vehicles is empty array, timeassigned exists).
    - `canceled`: `booking = 'canceled'` (vehicles exist, timeassigned is null).
    - `manual`: `source = 'manual'`.
    - `servedByVehicle`: `booking = 'served'`, grouped by `vehicles[array_lower(vehicles, 1)]`.
- Results are aggregated into a map keyed by date string.

### Data Model

```mermaid
erDiagram
  CALLS {
    uuid id PK
    timestamp timestamp
    timestamp timeanswered
    timestamp timeassigned
    text[] vehicles
    string source
  }
  STATS_CACHE_REDIS {
    string date_key PK "stats:YYYY:MM:DD"
    string answered
    string unanswered
    string served
    string unserved
    string canceled
    string manual
    string servedByVehicle "JSON string"
  }
  CALLS ..|> STATS_CACHE_REDIS : "aggregates_into"
```

### Error Handling

- Database query errors might result in missing data for a day or an empty response.
- Redis connection issues.

---

## Statistics - Fetch Detailed Statistics (Per Operator)

**Functional Description:**
Retrieves detailed call statistics for a specific day, broken down by individual operators. Also includes shift-based statistics if configured.

### API Interface

- **Protocol:** WebSocket
- **Event/Endpoint:** `stats:detailed`
- **Request:**

  ```json
  {
    "event": "stats:detailed",
    "data": {
      "year": "number",
      "month": "number",
      "date": "number"
    }
  }
  ```

- **Response (Unicast to self):**

  ```json
  {
    "event": "stats:detailed",
    "data": {
      "YYYY-MM-DD": { // Key is the date string
        // Per-operator stats
        "operator_id_1": {
          "answered": "string (count)",
          "unanswered": "string (count)",
          // ... other stats for this operator
        },
        "operator_id_2": { /* ...stats... */ },
        // Shift-based stats (if shifts are configured)
        "HH:MM - HH:MM": { // Key is shift range, e.g., "06:00 - 14:00"
          "answered": "string (count)",
          // ... other stats for this shift
        }
      }
    }
  }
  ```

- **Authentication:** WebSocket (Operator Session)

### Business Rules

- Backend (`stats.js`) processes the requested date.
- Calls `getDayStats(date, callStatuses, true, callback)` to get per-operator stats.
  - `getDayStats` (with `operator_id = true`) checks Redis for keys like `stats:YYYY:MM:DD:<status>:op`.
  - If not found in Redis (or for current day), `getDayStatsFromDB` is called with `operator_id = true`.
    - `getDayStatsFromDB` queries `calls` table, grouping by `operator_ids[array_lower/upper(operator_ids, 1)]` (first or last operator depending on status).
    - Results are cached in Redis under `stats:YYYY:MM:DD:<status>:op` HASH keys.
- If shifts are configured (`shifts` variable in `stats.js` is populated from `etc/stats.json`):
  - Calls `getDayShiftStats(date, callStatuses, null, callback)`.
    - `getDayShiftStats` determines shift boundaries for the given day.
    - For past days, it tries to fetch from Redis keys like `stats:YYYY:MM:DD:<status>:sh`.
    - If not in Redis or for current day, it queries `calls` table for each shift's time range.
    - Results for past days are cached in Redis.
- The per-operator stats and shift-based stats are merged into the response.

### Data Model

```mermaid
erDiagram
  CALLS {
    uuid id PK
    timestamp timestamp
    timestamp timeanswered
    timestamp timeassigned
    text[] vehicles
    string source
    int[] operator_ids
  }
  STATS_CACHE_REDIS_OP {
    string op_stats_key PK "stats:YYYY:MM:DD:<status>:op"
    map operator_counts "operator_id -> count"
  }
  STATS_CACHE_REDIS_SHIFT {
    string shift_stats_key PK "stats:YYYY:MM:DD:<status>:sh"
    map shift_counts "<shift_start>+<duration> -> count"
  }
  CALLS ..|> STATS_CACHE_REDIS_OP : "aggregates_into"
  CALLS ..|> STATS_CACHE_REDIS_SHIFT : "aggregates_into"
```

- `etc/stats.json` defines shift schedules.

### Error Handling

- Database or Redis errors might lead to incomplete or missing data.

```

---
**`docs/mapping.md`**
```markdown
# Mapping Features

## Mapping - Get Address from Coordinates (Reverse Geocoding)
**Functional Description:**
Converts geographic coordinates (latitude and longitude) into a human-readable address using an external geocoding service (e.g., Nominatim).

### API Interface
- **Protocol:** HTTP
- **Event/Endpoint:** External URL, configured via `mapConfig.API_URL.nominatim` (e.g., `https://nominatim.openstreetmap.org/reverse.php`)
- **Request:** (GET request with query parameters)
  - `lat`: number (latitude)
  - `lon`: number (longitude)
  - `format`: string (e.g., "json")
  - `addressdetails`: number (e.g., 1)
  - `zoom`: number (e.g., 18)
  - `accept-language`: string (e.g., "en", "mk")
- **Response:** (JSON object from the external service)
  ```json
  // Example Nominatim Response Snippet
  {
    "place_id": 297745578,
    "licence": "Data © OpenStreetMap contributors, ODbL 1.0. http://osm.org/copyright",
    "osm_type": "way",
    "osm_id": 4207841,
    "lat": "42.0000000",
    "lon": "21.4333333",
    "display_name": "Street Name, Number, Suburb, City, Postcode, Country",
    "address": {
      "road": "Street Name",
      "house_number": "Number",
      // ... other address components
      "city": "City",
      "country": "Country"
    }
    // ...
  }
  ```

- **Authentication:** Depends on the external service (likely none or API key if configured). The application itself doesn't add auth headers for this specific call in `RestService`.

### Business Rules

- The frontend `MapsComponent` calls `RestService.getAddress()`.
- `RestService` makes a direct HTTP GET request to the configured Nominatim URL.
- The `display_name` from the response is processed by the frontend to construct a shorter address string, typically up to the city name.
- This feature is client-driven; the backend's role is to provide the `mapConfig` (including API URLs) during `client:init`.

### Data Model

- N/A (Consumes an external service).
- `mapConfig` (provided by backend) stores the URL for the geocoding service.

### Error Handling

- HTTP errors from the external service (4xx, 5xx).
- Network connectivity issues on the client side.
- Client-side error handling in `RestService` or `MapsComponent` would manage these.

---

## Mapping - Get Locations from Address (Forward Geocoding/Search)

**Functional Description:**
Searches for geographic locations based on a textual query using an external geocoding service (e.g., Photon).

### API Interface

- **Protocol:** HTTP
- **Event/Endpoint:** External URL, configured via `mapConfig.API_URL.photon` (e.g., `https://photon.komoot.io/api/`)
- **Request:** (GET request with query parameters)
  - `q`: string (search query, e.g., "City, Street Name")
  - `lat`: number (latitude for search bias)
  - `lon`: number (longitude for search bias)
  - `lang`: string (e.g., "en")
  - `limit`: number (max number of results)
- **Response:** (JSON object from the external service)

  ```json
  // Example Photon Response Snippet
  {
    "features": [
      {
        "geometry": {
          "coordinates": [21.4333333, 42.0000000], // [lon, lat]
          "type": "Point"
        },
        "type": "Feature",
        "properties": {
          "osm_id": 12345,
          "osm_type": "N", // N: Node, W: Way, R: Relation
          "country": "Country Name",
          "osm_key": "highway",
          "city": "City Name",
          "osm_value": "residential",
          "postcode": "1000",
          "name": "Street Name",
          "state": "State/Region",
          "street": "Street Name",
          "housenumber": "12A" // Optional
        }
      }
      // ... more features
    ],
    "type": "FeatureCollection"
  }
  ```

- **Authentication:** Depends on the external service.

### Business Rules

- The frontend `MapsComponent` calls `RestService.getLocations()`.
- `RestService` makes a direct HTTP GET request to the configured Photon URL.
- The response (an array of features) is processed by `RestService`:
  - Extracts `coordinates` (lng, lat), `name`, and constructs a `description` (e.g., "osm_value, city" or "street housenumber, city").
- `MapsComponent` further filters these results:
  - Only locations within a configured radius (`mapConfig.radius`) from the map center (`mapConfig.center`) are kept.
- This feature is client-driven; the backend provides `mapConfig`.

### Data Model

- N/A (Consumes an external service).
- `mapConfig` (provided by backend) stores the URL and parameters for the geocoding service.

### Error Handling

- HTTP errors from the external service.
- Network connectivity issues on the client side.

```

---
**`docs/contacts.md`**
```markdown
# Contact Management Features

## Contact Management - Get Contact Info
**Functional Description:**
Retrieves detailed information for a contact based on their phone number, including name, historical call statistics (served, canceled, unserved), and common locations/destinations. This is often triggered when a call comes in or when an operator needs to view contact history.

### API Interface
- **Protocol:** WebSocket
- **Event/Endpoint:** `contact:info`
- **Request (Client → Server):**
  ```json
  {
    "event": "contact:info",
    "data": "string (phone number, e.g., '070123456')"
  }
  ```

- **Response (Server → Client, Unicast to requester):**

  ```json
  {
    "event": "contact:info",
    "data": {
      "<phone_number>": { // The phone number requested
        "name": "string (optional, contact's name)",
        "served": "number (optional, count of served calls)",
        "canceled": "number (optional, count of canceled calls)",
        "unserved": "number (optional, count of unserved calls)",
        "location": "Array<string> (optional, common pickup locations, may include area like 'Street | Area')",
        "destination": "Array<string> (optional, common drop-off destinations)",
        "firstcontact": "number (optional, epoch ms of first recorded interaction)",
        "lastaction": "number (optional, epoch ms of last recorded action)",
        "lang": "string (optional, preferred language for contact if configured)"
        // Potentially other fields like 'finaldestination'
      }
    }
  }
  ```

  Additionally, when calls are loaded (`call:init`, `call:list`), a bulk `contact:info` event might be sent containing info for all unique phone numbers in the call list.
- **Authentication:** WebSocket (Operator Session)

### Business Rules

- Backend (`contacts.js` `getDetails` and `getInfo` functions):
  - **Cache First (Redis):**
    - Checks Redis HASH `contacts:<phone>` for summary info (name, served, canceled, unserved, firstcontact, lastaction, lang, finaldestination).
    - Checks Redis SORTED SET `contacts:<phone>:location` for common locations (scored by frequency).
    - Checks Redis SORTED SET `contacts:<phone>:destination` for common destinations (scored by frequency).
    - Checks Redis STRING `contacts:<phone>:assigned` for details of the last assigned order.
  - **Database Fallback (PostgreSQL):**
    - If data is not fully available or up-to-date in Redis (e.g., first time this contact is queried or cache expired):
      - Queries `calls` table in PostgreSQL.
      - `allLocations`: Aggregates all unique `location` and `area` combinations, counting occurrences and noting if they were part of a booked call (`timeassigned IS NOT NULL AND vehicles IS NOT NULL`). Sorted by booking status then count.
      - `count.served`: Sums lengths of `vehicles` arrays where `timeassigned IS NOT NULL AND vehicles <> '{}'`.
      - `count.canceled`: Sums lengths of `vehicles` arrays where `timeassigned IS NULL AND ARRAY_LENGTH(vehicles, 1) IS NOT NULL`.
      - `count.unserved`: Counts calls where `vehicles = '{}'`.
      - `getWeightedList` for `destination`: Aggregates unique `destination` strings, counting occurrences from booked calls.
      - Determines `firstcontact` timestamp from the earliest call.
    - **Cache Update:** Populates/updates the Redis cache structures mentioned above with the data retrieved from PostgreSQL.
  - **Last Active Order Logic:** If the last assigned order for this contact (from `contacts:<phone>:assigned` in Redis) is still within an "active window" (based on `timeassigned`, `arrivaltime`, and `activeCallTimeout`), its details (location, destination, vehicles, relative arrival time) are prioritized as defaults for a new call.
  - Otherwise, the most frequent booked location/destination (from sorted sets) might be used as defaults.
- The `ContactsService` on the client manages a local cache and a callback queue for pending requests to avoid redundant server calls.

### Data Model

```mermaid
erDiagram
  CALLS {
    uuid id PK
    timestamp timestamp
    timestamp timeassigned
    string phone
    text location
    text destination
    text area
    text[] vehicles
  }
  CONTACTS_CACHE_REDIS {
    string phone PK "Key: contacts:<phone>"
    string name
    int served_count
    int canceled_count
    int unserved_count
    timestamp firstcontact
    timestamp lastaction
    string lang
    string last_assigned_call_details "JSON string"
    zset common_locations
    zset common_destinations
  }
  CALLS o--o{ CONTACTS_CACHE_REDIS : "aggregates_to"
```

### Error Handling

- If contact not found in Redis and DB query fails, an error might be passed to the callback, or an empty object returned.
- Timeout for Redis/DB operations.

---

## Contact Management - Set Contact Name

**Functional Description:**
Allows an operator to assign or update the name associated with a phone number.

### API Interface

- **Protocol:** WebSocket
- **Event/Endpoint:** `contact:update`
- **Request (Client → Server):**

  ```json
  {
    "event": "contact:update",
    "data": {
      "<phone_number>": { // e.g., "070123456"
        "name": "string (the new name for the contact)"
      }
    }
  }
  ```

- **Response (Server → Clients, Broadcast):**

  ```json
  {
    "event": "contact:update",
    "data": { // Same as request body
      "<phone_number>": {
        "name": "string (the new name)"
      }
    }
  }
  ```

- **Authentication:** WebSocket (Operator Session)

### Business Rules

- Backend (`contacts.js` `updateInfo` function, called via `eventHandlers.js`):
  - Receives the phone number and the new name.
  - Updates the `name` field in the Redis HASH `contacts:<phone_number>`.
  - Broadcasts the `contact:update` event to all connected clients so their UIs can reflect the change.
- Client-side (`PhoneComponent`):
  - Prompts the user for a name.
  - If the name is changed, sends the `contact:update` event.
  - Subscribes to `contact:update` events to refresh displayed names.

### Data Model

```mermaid
erDiagram
  CONTACTS_CACHE_REDIS {
    string phone PK "Key: contacts:<phone>"
    string name
  }
```

### Error Handling

- Issues saving to Redis.
- The current backend implementation doesn't explicitly send an error back to the client for this specific operation if Redis fails, but it would log it.

```

---
**`docs/system-events.md`**
```markdown
# System & Miscellaneous Events

## System - WebSocket Keep-Alive (Ping/Pong)
**Functional Description:**
Maintains the WebSocket connection liveness through a ping/pong mechanism initiated by both client and server to detect and handle stale connections.

### API Interface
- **Protocol:** WebSocket
- **Event/Endpoint (Client → Server):** `ping`
- **Request (Client → Server):**
  ```json
  {
    "event": "ping",
    "data": "string (timestamp_string, e.g., ISOString)"
  }
  ```

- **Response (Server → Client, to client's ping):**

  ```json
  {
    "event": "pong",
    "data": "string (timestamp_string, echoed from client's ping)"
  }
  ```

- **Event/Endpoint (Server → Client):** `ping`
- **Request (Server → Client):**

  ```json
  {
    "event": "ping",
    "data": "string (timestamp_string, e.g., ISOString or epoch ms)"
  }
  ```

- **Response (Client → Server, to server's ping):**

  ```json
  {
    "event": "pong",
    "data": "string (timestamp_string, echoed from server's ping)"
  }
  ```

- **Authentication:** N/A (Protocol level)

### Business Rules

- **Client-Side (`SocketService.ts`):**
  - If `keepAlive.enabled` is true in config.
  - Periodically sends a `ping` event with the current timestamp to the server (`config.keepAlive.pingInterval`).
  - Expects a `pong` event from the server within `config.keepAlive.pongTimeout`.
  - If `pong` is not received in time, the client considers the connection stale, closes it, and attempts to reconnect.
- **Server-Side (`websocket.js`, `operatorServer.js`):**
  - Upon receiving a `ping` from a client, it immediately responds with a `pong` echoing the `data`.
  - The server also has its own ping mechanism (`OperatorServer` in `operatorServer.js`):
    - If a client (operator) is already connected and another connection attempt for the same operator ID comes in, the server sends a `ping` to the existing client.
    - If the existing client responds with `pong` within `options.pongTimeout`, the new connection is rejected.
    - If no `pong` is received, the old client is considered stale/terminated, and the new connection is allowed.
- This dual ping/pong ensures both sides can detect a broken connection.

### Data Model

- N/A

### Error Handling

- Timeout waiting for `pong` leads to connection closure and reconnection attempts.

---

## System - Client Reload Instruction

**Functional Description:**
Server can instruct the client application to reload itself, typically used after deploying a new client version to ensure clients are up-to-date.

### API Interface

- **Protocol:** WebSocket (Server Push)
- **Event/Endpoint:** `client:reload`
- **Request (N/A - Server Initiated):**
- **Response (Server → Client, Unicast or Broadcast):**

  ```json
  {
    "event": "client:reload",
    "data": "string (new client version, e.g., '1.4.0')"
  }
  ```

- **Authentication:** N/A

### Business Rules

- **Server-Side (`operatorServer.js`):**
  - During WebSocket connection handshake (`verifyClient`), the server compares the client's version (from `?v=<version>` URL query parameter) with its configured `options.clientVersion`.
  - If `semver.lt(location.query.v, this.options.clientVersion)` (client version is less than server's expected client version), the server sends the `client:reload` event with the new version.
  - The server then closes the WebSocket connection with code 1011 ("incompatible version").
- **Client-Side (`SocketService.ts`):**
  - Upon receiving the `client:reload` event, the client executes `location.reload(true)` to force a full page reload.

### Data Model

- N/A

### Error Handling

- Client might fail to reload if there are JavaScript errors preventing `location.reload()`.

---

## System - Server Error Reporting to Client

**Functional Description:**
Server reports specific operational errors related to client requests back to the originating client. This allows the client to inform the user or take corrective action.

### API Interface

- **Protocol:** WebSocket (Server Push)
- **Event/Endpoint:** `server:error`
- **Request (N/A - Server Initiated):**
- **Response (Server → Client, Unicast to relevant client):**

  ```json
  {
    "event": "server:error",
    "data": {
      "event": "string (original event name that caused error, e.g., 'call:edit')",
      "data": "any (original data from the failed request, optional, e.g., call_id)",
      "key": "string (e.g., temp_id for messages, optional)",
      "msg": "string (descriptive error message, e.g., 'This call is already locked')"
    }
  }
  ```

- **Authentication:** N/A (Sent to a specific client session that triggered the error)

### Business Rules

- Backend (`eventHandlers.js` `clientError` function, or specific error paths in handlers):
  - When an error occurs while processing a client's WebSocket request (e.g., trying to update a call not locked by the operator, failing to send an SMS).
  - The server constructs the `server:error` payload, including the original event and a message.
  - It sends this event back to the client whose request caused the error.
- **Client-Side (e.g., `MessagesService.ts`, `CallEditService.ts`):**
  - Services subscribe to `socket.dataStream`.
  - If a `server:error` event is received, they typically:
    - Display a notification/toast to the user (e.g., via `NotificationService`).
    - Revert any optimistic UI updates (e.g., clear a "sending SMS" spinner).

### Data Model

- N/A

### Error Handling

- This *is* the error handling mechanism for many WebSocket operations.

---

## System - Client Error Reporting to Server

**Functional Description:**
Client application can report internal errors (e.g., failure to parse a WebSocket message from the server) to the server, primarily for logging and debugging purposes.

### API Interface

- **Protocol:** WebSocket
- **Event/Endpoint:** `client:error`
- **Request (Client → Server):**

  ```json
  {
    "event": "client:error",
    "data": {
      "info": "string (error context, e.g., 'JSON.parse error')",
      "msg": "string (error message, e.g., e.name + \": \" + e.message)",
      "data": "any (problematic data received from server, optional)"
    }
  }
  ```

- **Response (Server → Client):** None direct. Server logs the error.
- **Authentication:** WebSocket (Operator Session or Anonymous)

### Business Rules

- **Server-Side (`websocket.js`):**
  - The `on('message')` handler for WebSocket connections has a `try...catch` block for `JSON.parse(data)`.
  - If parsing fails, or if a received message lacks the `event` field, the server logs this error and *could* (though not explicitly shown sending back to client in this path) construct a `client:error` to itself for logging.
  - The `eventHandlers.js` also has a `client:error` handler which currently just logs if `debugMode` is on.
- **Client-Side (`SocketService.ts`):**
  - The provided Angular `SocketService` does not explicitly show it *sending* a `client:error` event. This event seems to be more for the server to log issues with incoming client messages or for the server to report its own internal errors *about* a client's message structure.

### Data Model

- N/A

### Error Handling

- Server logs these errors. If the error is critical on the client, it might lead to a UI issue or a WebSocket reconnection attempt by the client.

---
**Recommended Next Steps (from your prompt, slightly expanded):**

1. **Create Feature Modules:** Based on the domain divisions (`call-management`, `scheduling`, etc.), create corresponding Angular modules. This will help in organizing components, services, and routing related to each domain.
2. **Implement Shared WebSocket Service:** Develop a robust `SocketService` in Angular.
    - Use RxJS Subjects/Observables for event streams (`dataStream`, `initStream`, `disconnectStream`).
    - Provide strongly-typed methods for sending specific events (e.g., `sendCallUpdate(data: CallUpdatePayload)`).
    - Handle automatic reconnection logic and keep-alive (ping/pong).
3. **Generate TypeScript Interfaces:** Based on the JSON request/response examples in these Markdown files (and the `openapi.yaml`), create TypeScript interfaces for all payloads. This will provide type safety and improve developer experience.
    - Example: `interface Call { id: string; timestamp: number; ... }`, `interface ClientInitPayload { apps: string[]; ... }`.
4. **Define Redis Schema/Access Patterns:** Based on the data model relationships and how Redis is used (caching, sorted sets for contact history, HASHes for contact summaries, pub/sub for real-time updates if needed beyond current scope):
    - Document key naming conventions (e.g., `contacts:<phone>`, `schedule:<id>`).
    - Define data structures for HASH fields.
    - This is already partially covered in `data-models.md`.
5. **Create Migration Scripts for PostgreSQL Entities:**
    - Use a migration tool (e.g., TypeORM migrations, Knex.js migrations, Flyway, Liquibase).
    - Write scripts to create/alter tables like `operators`, `calls` based on the `data-models.md`.
    - Define appropriate data types, constraints (PK, FK, NOT NULL), and indexes.
6. **Implement Global Exception Handling:**
    - **Angular Client:** Create an `HttpInterceptor` to catch HTTP errors globally and display user-friendly messages or redirect. Handle WebSocket `server:error` events in relevant services.
    - **Node.js Backend (New):** Implement a global exception filter (e.g., Express middleware or framework-specific like NestJS ExceptionFilter) to catch custom `HttpException` instances and format consistent error responses.
7. **Authentication & Authorization (Rewrite Focus):**
    - For the new backend, consider a more standard token-based authentication (e.g., JWT) for HTTP APIs if they expand beyond simple GETs.
    - WebSocket authentication can remain similar (operator_id in URL) or be upgraded to pass a token after initial HTTP login.
    - Implement clear authorization rules (e.g., an operator can only lock/unlock calls they are assigned or have permissions for).
8. **Configuration Management:** Centralize backend configuration (DB connections, API keys, feature flags) using environment variables and/or dedicated config files, loaded at startup.
9. **Logging & Monitoring:** Implement comprehensive logging on the new backend (e.g., using Winston or Pino) and consider setting up monitoring/alerting.
10. **Testing Strategy:** Plan for unit, integration, and end-to-end tests for both frontend and backend.
